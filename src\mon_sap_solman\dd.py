"""Details Design module for mon-sap-solman."""

import json
from datetime import datetime, timedelta

import pandas as pd
from sqlalchemy.orm import Session

from mon_sap_solman import utils
from olympus_common import db, enums
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Make the dataframe transformation corresponding to the details design."""
    # hardcoded values
    df["agent_id"] = agent_id
    df["manager"] = _manager()
    df["node"] = _node()
    df["action_class"] = _scope()
    df["top_level"] = _top_level()
    df["handle_time"] = _handle_time()

    # computed values
    df["actionable"] = df.apply(_actionable_alarm, axis=1)
    df["additional_data"] = df.apply(_additional_data, axis=1)
    df["wake_up_time"] = df.apply(_wake_up_time, axis=1)
    df["ci_id"] = df.apply(_ci_id, axis=1)
    df["clear_type"] = df.apply(_clear_type, axis=1)
    df["event_id"] = df.apply(_event_id, axis=1)
    df["node_alias"] = df.apply(_node_alias, axis=1)
    df["raise_time"] = df.apply(_raise_time, axis=1)
    df["clear_time"] = df.apply(_clear_time, axis=1)
    df["metric_name"] = df.apply(_metric_name, axis=1)
    df["metric_type"] = df.apply(_metric_type, axis=1)
    df["summary"] = df.apply(_summary, axis=1)
    df["event_type"] = df.apply(_clear_level, axis=1)
    df["severity"] = df.apply(_severity, axis=1)

    return df


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the agent."""
    agent_name = agent()
    agent_id = db.Agent.get_agent_id_from_name(agent_name, session)
    df = transform(df, agent_id)
    return df


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def agent() -> str:
    """Return the agent name for the DD."""
    return "SolMan"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable_alarm(row: pd.Series) -> bool | None:
    """Return the actionable alarm value."""
    if _is_heartbeat_event(row):
        return False

    return None  # by default it needs enrichment


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _additional_data(row: pd.Series) -> str | None:
    """Return the additional data."""
    if row["r3maiAlertMOType"] == "BPMON_OBJ":
        return None
    else:
        return json.dumps({"additional_info": row["r3maiAlertName"] + "." + str(row["r3maiAlertMetricValue"])})


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _delay() -> int:
    """Return the delay for Solman events snooze mechanism.

    Currently all the events should be snooze during 300 seconds.
    """
    return 300


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Return the CI ID value."""
    alert_mon_name: str = row["r3maiAlertMOName"]

    if _is_heartbeat_event(row):
        return "Solman_HB"

    if not (
        row["r3maiAlertTechnicalName"].startswith("FILE_SYSTEM")
        or row["r3maiAlertTechnicalName"].startswith("HOST_AVAILABILITY")
    ):
        return alert_mon_name[:3]
    else:
        if row["r3maiAlertMOType"] == "BPMON_OBJ":
            return utils.substring_before_second(test_string=alert_mon_name, delimiter="_")
        else:
            return alert_mon_name


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type(row: pd.Series) -> str:
    """Return the type of clear for Solman events."""
    if _is_heartbeat_event(row):
        return enums.ClearType.MANUALLY.value

    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_id(row: pd.Series) -> str:
    """Return the event id."""
    return row["r3maiAlertMOId"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the manager for SAP Solman DD."""
    return "mon-sap-solman"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node() -> str:
    """Return the node value."""
    return agent()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node_alias(row: pd.Series) -> str:
    """Return the node alias value."""
    return row["host"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime:
    """Return the raise time (value from the EMS)."""
    if _is_heartbeat_event(row):
        return olympus_utils.parse_datetime(row["messageTimestamp"])

    date_str: str = row["r3maiAlertDate"]  # YYYYMMDD
    time_str: str = row["r3maiAlertTime"]  # hhmmss
    timestamp = datetime.strptime(f"{date_str} {time_str}", "%Y%m%d %H%M%S")
    return timestamp


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime:
    """Return the wake up time of the events (value from when the event is received by Olympus)."""
    return _raise_time(row) + timedelta(0, _delay())


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> datetime | None:
    """Return the clear time of the events (value from when the event is received by Olympus)."""
    if _clear_level(row) == enums.AlarmType.RESOLUTION.value:
        return _handle_time()
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _is_heartbeat_event(row: pd.Series) -> bool:
    """Return if the event is a heartbeat event."""
    alert_mon_name: str = row["r3maiAlertMOName"]
    if alert_mon_name.endswith("_HEARTBEAT"):
        return True
    else:
        return False


def get_server_heartbeat_name(row: pd.Series) -> str:
    """Return the server heartbeat name."""
    alert_mon_name: str = row["r3maiAlertMOName"]
    return alert_mon_name.split("_")[0]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Return the scope of the events for SAP Solman."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type(row: pd.Series) -> str:
    """Return the metric type for the events."""
    if _is_heartbeat_event(row):
        return "/ApplicationEvent/"
    else:
        return "/SAPEvent/"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str:
    """Return the metric name for the event."""
    if _is_heartbeat_event(row):
        return "Heartbeat"

    if row["r3maiAlertMOType"] == "BPMON_OBJ":
        alert_mon_name: str = row["r3maiAlertMOName"]
        return utils.substring_after_second(alert_mon_name, "_")
    else:
        return row["r3maiAlertTechnicalName"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _top_level() -> str | None:
    """Return the top level for SAP Solman."""
    return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Return the summary for the event."""
    if row["r3maiAlertMOType"] == "BPMON_OBJ":
        return row["r3maiAlertMOName"]
    elif _is_heartbeat_event(row):
        server_name: str = get_server_heartbeat_name(row)
        return f"SAP probe: {server_name} Heartbeat Message"
    else:
        return (
            f"{row['r3maiAlertName']}.{row['r3maiAlertMetricValue']}"
            if str(row["r3maiAlertMetricValue"]) != "0"
            else row["r3maiAlertName"]
        )


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_level(row: pd.Series) -> str:
    """Return the alarm type (clear/alarm) for the event."""
    if _is_heartbeat_event(row):
        return enums.AlarmType.HEARTBEAT.value

    match row["r3maiAlertStatus"]:
        case "O":
            return enums.AlarmType.PROBLEM.value
        case "C":
            return enums.AlarmType.RESOLUTION.value
        case _:
            # Since this shouldn't occur we make those alarms as a clear to avoid showing them to the ICC.
            return enums.AlarmType.RESOLUTION.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Return the severity for the event."""
    severity_: str = str(row["r3maiAlertSeverity"])

    if _is_heartbeat_event(row):
        return enums.Severity.INDETERMINATE.value

    if _clear_level(row) == enums.AlarmType.RESOLUTION.value:
        return enums.Severity.CLEARED.value

    if severity_.isdigit():
        int_severity = int(severity_)
        if int_severity >= 0 and int_severity <= 6:
            return enums.Severity.MAJOR.value
        elif int_severity >= 7 and int_severity <= 9:
            return enums.Severity.CRITICAL.value
        else:
            return enums.Severity.INDETERMINATE.value
    else:
        return enums.Severity.INDETERMINATE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _handle_time() -> datetime:
    """Create the handle time for the event."""
    return olympus_utils.now_naive()
