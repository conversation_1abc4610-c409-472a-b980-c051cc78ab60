"""Details Design module for mon-gsx."""

import json
from datetime import datetime

import pandas as pd
from sqlalchemy.orm import Session

from mon_gsx import utils
from olympus_common import db, enums
from olympus_common import utils as olympus_utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable_alarm(row: pd.Series) -> bool:
    """Return the actionable alarm value for the event."""
    clear_level = _clear_level(row)
    if utils.is_heartbeat(row):
        return False
    else:
        match clear_level:
            case enums.AlarmType.PROBLEM.value:
                return True
            case enums.AlarmType.RESOLUTION.value:
                return True
            case _:
                # Deduced from DD
                return False


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _additional_data(row: pd.Series) -> str:
    """Return the additional data."""
    return json.dumps({"trap_message": row["trapMessage"]})


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _agent() -> str:
    """Return the agent for the DD."""
    return "GSX_Monitor"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Return the ci_id concerning the event."""
    if utils.is_heartbeat(row):
        return "GSX_HB"

    return f"GSX_{row['gsxStationName']}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type(row: pd.Series) -> str:
    """Return the clear type for the DD."""
    return enums.ClearType.MANUALLY.value if utils.is_heartbeat(row) else enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type(row: pd.Series) -> str:
    """Return the metric type for the DD."""
    if utils.is_heartbeat(row):
        return "/ApplicationEvent/"

    return "/ServiceAvailability/"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str:
    """Return the metric name concerning the event."""
    if utils.is_heartbeat(row):
        return "Heartbeat"

    trap_message: str = row["trapMessage"]
    useful_data = trap_message.split("(Exchange ", maxsplit=1)
    if len(useful_data) >= 2:
        metric_name = useful_data[1].split(")", maxsplit=1)[0]
        return metric_name.replace(" ", "_")
    else:
        return "N/A"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the manager for the DD."""
    return "mon-gsx"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Return the node concerning the event."""
    return row["gsxStationName"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node_alias(row: pd.Series) -> str:
    """Return the node alias concerning the event."""
    return row["gsxStationName"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime:
    """Return the raise time for the DD events."""
    return olympus_utils.parse_datetime(row["timestamp"])


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> datetime | None:
    """Return the clear time for the event."""
    return row["raise_time"] if _clear_level(row) == enums.AlarmType.RESOLUTION.value else None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Return the scope of the EMS."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Return the severity of the event."""
    if utils.is_heartbeat(row):
        return enums.Severity.INDETERMINATE.value
    elif _clear_level(row) == enums.AlarmType.PROBLEM.value:
        return enums.Severity.CRITICAL.value
    elif _clear_level(row) == enums.AlarmType.RESOLUTION.value:
        return enums.Severity.INDETERMINATE.value
    else:
        return enums.Severity.INDETERMINATE.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Return the summary of the event."""
    trap_message: str = row["trapMessage"]
    if utils.is_heartbeat(row):
        return f"{_agent()}  Heartbeat Message"
    else:
        return trap_message.split(" - ", maxsplit=1)[0]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_level(row: pd.Series) -> str:
    """Return the clear level of the event."""
    if utils.is_heartbeat(row):
        return enums.AlarmType.HEARTBEAT.value

    match row["status"]:
        case "Down":
            return enums.AlarmType.PROBLEM.value
        case "Back Online":
            return enums.AlarmType.RESOLUTION.value
        case _:
            # Shouldn't occur so set as resolution to not show error on this
            return enums.AlarmType.RESOLUTION.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _top_level(row: pd.Series):
    """Return the top level of the event."""
    return "A1634" if utils.is_heartbeat(row) else "A1635"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _handle_time():
    """Return the handle time for the DD events."""
    return olympus_utils.now_naive()


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the GSX Details Design."""
    agent_id = db.Agent.get_agent_id_from_name("GSX_Monitor", session)

    return _transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Make the dataframe transformation corresponding to the details design."""
    # Hardcoded values
    df["agent_id"] = agent_id
    df["manager"] = _manager()
    df["action_class"] = _scope()
    df["handle_time"] = _handle_time()

    # Computed values
    df["raise_time"] = df.apply(_raise_time, axis=1)
    df["actionable"] = df.apply(_actionable_alarm, axis=1)
    df["additional_data"] = df.apply(_additional_data, axis=1)
    df["ci_id"] = df.apply(_ci_id, axis=1)
    df["clear_type"] = df.apply(_clear_type, axis=1)
    df["metric_type"] = df.apply(_metric_type, axis=1)
    df["metric_name"] = df.apply(_metric_name, axis=1)
    df["node"] = df.apply(_node, axis=1)
    df["node_alias"] = df.apply(_node_alias, axis=1)
    df["clear_time"] = df.apply(_clear_time, axis=1)
    df["severity"] = df.apply(_severity, axis=1)
    df["summary"] = df.apply(_summary, axis=1)
    df["event_type"] = df.apply(_clear_level, axis=1)
    df["top_level"] = df.apply(_top_level, axis=1)

    return df
