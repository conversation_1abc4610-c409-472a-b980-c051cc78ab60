"""Module to provide different datareaders used in the olympus project."""

import json
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Sequence, TypeVar

from confluent_kafka import Consumer, KafkaError, TopicPartition
from sqlalchemy import Select, and_, select
from sqlalchemy.orm import Session, aliased

from olympus_common import db, elastic, statics
from olympus_common._protocols import KafkaConsumer, KafkaMessage
from olympus_common.config import ElasticConfig, KafkaConsumerConfig
from olympus_common.dataclass import dataclass_field, env_field
from olympus_common.elastic_apm import CaptureSpan, trace_scan
from olympus_common.enums import AlarmJobStatus, MeasureType
from olympus_common.exceptions import OlympusError
from olympus_common.utils import now_naive


class DataReader(ABC):
    """Represent the base DataReader."""

    @abstractmethod
    def read_data(self, *args, **kwargs) -> list[dict]:
        """Read data from the current data source."""
        pass

    @abstractmethod
    def success(self, data: list[dict]) -> None:
        """Define what to do when the provided data was parsed successfully.

        This function should be called in Application._run when the wrapped function returns without error.
        """
        pass

    @abstractmethod
    def error(self, data: list[dict], exc: Exception) -> None:
        """Define what to do when the retrieved data was NOT parsed successfully.

        This function should be called in Application._run when the wrapped function returns with an error.
        """
        pass


@dataclass
class Checkpoint:
    """Represent a checkpoint."""

    path: Path
    filename: str = "checkpoint.txt"
    value: str = ""

    @property
    def fullpath(self) -> Path:
        """Return the full path to the configured checkpoint."""
        return self.path / self.filename

    def read(self) -> str:
        """Read the checkpoint.

        Update the `value` attribute and return it.

        When a FileNotFoundError would be raised, the value is updated to an empty string.
        """
        try:
            value = self.fullpath.read_text()
        except FileNotFoundError:
            value = ""

        self.value = value
        return self.value

    def write(self, value: str = "") -> None:
        """Write the provided value to the checkpoint.fullpath.

        The `value` attribute on `self` is not updated in this function.

        If no value is provided, `self.value` is used. (Only useful if you set `value` manually)
        This method will ensure that the checkpoint's Path fully exists.
        """
        if not value:
            value = self.value
        self.fullpath.parent.mkdir(exist_ok=True, parents=True)
        self.fullpath.write_text(value)


@dataclass
class ElasticReader(DataReader):
    """Represent an ElasticReader.

    This concrete DataReader reads data from the configured Elastic and prints on success and error.
    """

    query: dict[str, Any]
    sort_query: tuple[dict[str, dict[str, str]], dict[str, str]]
    checkpoint: Checkpoint | None = None
    config: ElasticConfig = dataclass_field(ElasticConfig)

    def read_data(self) -> list[dict]:
        """Read data from the configured indexes.

        As long as checkpoint.value is empty and no checkpoint exists, this will retrieve data with the original
        configured `self.query`. Once `self.success` is called, the checkpoint will be created and `self.query` will
        only fetch since that checkpoint, even after restarts.
        """
        msg = f"Getting data for {type(self).__name__}"
        if self.checkpoint:
            self.checkpoint.read()
            self._update_query_filter()
            msg += f" with {self.checkpoint.value=}"
        logging.debug(msg)
        return self._get_data()

    def success(self, data: list[dict]) -> None:
        """Update `self.checkpoint.value` with the max timestamp of the provided data and write it as checkpoint.

        Notes
        -----
        Each item in the provided `data` list is assumed to contain the key `_source` which is another dictionary with
        at least the key `@timestamp`.
        """
        if not self.checkpoint:
            return  # Do nothing on success in case no checkpoint is used.
        max_timestamp_item = max(data, key=lambda item: item["_source"]["@timestamp"])
        new_checkpoint_value = str(max_timestamp_item["_source"]["@timestamp"])
        self.checkpoint.write(new_checkpoint_value)

    def error(self, _: list[dict], __: Exception) -> None:
        """Do nothing on error."""

    def _update_query_filter(self) -> None:
        """Update the config's query_filter if `self.checkpoint.value` has a truthy value."""
        # * Only update if `self.checkpoint.value` is truthy. (we never want to set the query's value to empty-string)
        if not self.checkpoint or not self.checkpoint.value:
            return

        # * Replace this time into the correct place in `self.query`
        self.query["bool"]["filter"][0]["range"]["@timestamp"]["gt"] = self.checkpoint.value
        logging.debug(f"Updated query: {self.query}")

    def _get_data(self) -> list:
        """Return all elastic data corresponding to the current config."""
        data = []
        for index in self.config.indexes:
            try:
                index_data = elastic.read_elastic_index(
                    self.config.user,
                    self.config.password,
                    self.config.servers,
                    index,
                    self.query,
                    self.sort_query,
                    10000,
                    "1m",
                )
            except ValueError as exc:
                logging.debug(f"Exception while reading elastic index: {exc}")
                index_data = []
            data.extend(index_data)
        return data


@dataclass
class DatabaseReader(DataReader):
    """Represent a database reader for alarms."""

    session: Session
    job_name: str

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def read_data(self) -> list[dict]:
        """Read data from an alarm in database.

        Notes
        -----
        Alarms will be enriched, active or not
        Only active alarms will be sent to the UI
        """
        statement: Select  # Type statement as a generic Select to enable re-using the variable-name in the if/else
        if self.job_name == "enrichment":
            statement = (
                select(db.Alarm, db.AlarmJob.job_status)
                .join(db.AlarmJob, and_(db.Alarm.id == db.AlarmJob.alarm_id, db.AlarmJob.job_name == self.job_name))
                .where(db.AlarmJob.get_default_where_clause(self.job_name))
                .where(db.Alarm.wake_up_time <= now_naive())
                .limit(statics.MAX_ALARM_LIMIT)  # protect against overload of enrichments
                .order_by(db.Alarm.is_active.desc())  # Active alarms are more urgent than the closed ones.
                .order_by(db.AlarmJob.job_status.asc())
                .distinct()
            )
        elif self.job_name == "ui_sending":
            enrichment_job = aliased(db.AlarmJob, name="enrichment_job")
            statement = (
                select(db.Alarm, db.AlarmJob.job_status)
                .join(db.AlarmJob, and_(db.Alarm.id == db.AlarmJob.alarm_id, db.AlarmJob.job_name == self.job_name))
                .join(
                    enrichment_job,
                    and_(db.Alarm.id == enrichment_job.alarm_id, enrichment_job.job_name == "enrichment"),
                )
                .where(db.Alarm.is_active == 1)
                .where(db.AlarmJob.get_default_where_clause(self.job_name))
                .where(enrichment_job.job_status.in_([AlarmJobStatus.DONE.value, AlarmJobStatus.NOT_DONE.value]))
                .order_by(db.AlarmJob.job_status.asc())
                .limit(statics.MAX_ALARM_LIMIT)  # protect against overload of ui_sending
                .distinct()
            )
        elif self.job_name == "agent_heartbeat":
            statement = select(db.AgentHeartbeat).where(db.AgentHeartbeat.is_active)
        else:
            return []

        data: Sequence[db.Base] | None = self.session.execute(statement).scalars().all()
        if not data:
            return []
        data_dict = [result.asdict() for result in data]

        return data_dict

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def success(self, data: list[dict]) -> None:
        """Update the alarmjobs in case of success."""
        self._update_alarm_job(data)

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def error(self, data: list[dict], exc: Exception) -> None:
        """Update the alarmjobs in case of error."""
        self._update_alarm_job(data, exc)

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def _update_alarm_job(self, data: list[dict], exc: Exception | None = None) -> None:
        if self.job_name == "agent_heartbeat":
            return
        for record in data:
            alarm_job = db.AlarmJob(alarm_id=record["id"], job_name=self.job_name, job_time=now_naive())

            if record["process_status"] == AlarmJobStatus.IN_ERROR.value:
                alarm_job.job_status = AlarmJobStatus.IN_ERROR.value
                alarm_job.job_error = record["process_error"]
                if not alarm_job.job_error and exc:
                    alarm_job.job_error = str(exc)
            elif record["process_status"] == AlarmJobStatus.DONE.value:
                alarm_job.job_status = AlarmJobStatus.DONE.value
            elif record["process_status"] == AlarmJobStatus.NOT_DONE.value:
                alarm_job.job_status = AlarmJobStatus.NOT_DONE.value
            else:
                raise OlympusError("Unknown process status")

            alarm_job.update(self.session)

            if self.job_name == "enrichment":
                if alarm_job.job_status in (AlarmJobStatus.DONE.value, AlarmJobStatus.NOT_DONE.value):
                    ui_sending_job = db.AlarmJob(job_name="ui_sending", job_status=AlarmJobStatus.TO_DO)
                    ui_sending_job.alarm_id = alarm_job.alarm_id
                    ui_sending_job.insert_object(self.session)
            elif self.job_name == "ui_sending":
                self._reenable_and_resend_alarm(record)

        self.session.commit()  # No need to flush() as it's part of the commit().

    @CaptureSpan(span_type=MeasureType.DB_QUERY.value)
    def _reenable_and_resend_alarm(self, initial_alarm_state: dict) -> bool:
        """Re-enable and resend an alarm.

        If a new occurrence was associated with the alarm during the processing,
        re-enable the alarm and resend it. We can distinguish the following cases:
            a) Clear received quickly on top of an existing problem.
            b) Clear received quickly on top of an existing clear (no need to resend the alarm so we avoid it).
            c) Problem received on top of an existing problem (resend to forward the correct last occurrence
                time and tally).
            d) Problem received on top of an existing clear (resend to re-enable the alarm).

        Returns True if a re-send was made, in which case further updates must not be done.
        """
        alarm = db.Alarm.get_alarm_by_id(self.session, initial_alarm_state["id"])
        if alarm and alarm.last_occurrence_id != initial_alarm_state["last_occurrence_id"]:
            if not alarm.is_active and alarm.last_occurrence_id != alarm.last_clear_id:
                # Case d) verifies the condition, while case b) does not.
                # We set this condition to avoid making updates on Alarm as much as possible.
                alarm.is_active = True
                alarm.update_object(self.session)

            alarm.resend_to_ui(self.session)
            return True

        return False


@dataclass
class LocalStorageReader(DataReader):
    """Represent a LocalStorageReader.

    This concrete DataReader reads data from `self.filepath` and prints on success and error.
    """

    filepath: Path

    def read_data(self) -> list[dict]:
        """Read data from `self.filepath` or return an empty list if the file does not exist."""
        try:
            text = self.filepath.read_text()
        except FileNotFoundError:
            return []
        return json.loads(text)

    def success(self, data: list[dict]) -> None:
        """Print data on success."""
        print(self, "success", data)

    def error(self, data: list[dict], exc: Exception) -> None:
        """Print data on error."""
        print(self, "error", data, exc)


@dataclass
class PartitionOffset:
    """Represent a partition offset."""

    topic_name: str = env_field("SINGLE_TOPIC_NAME", default="")
    partition_number: int = env_field("SINGLE_PARTITION_NUMBER", astype=int, default="-1")
    offset: int = env_field("SINGLE_OFFSET", astype=int, default="-1")

    def is_enabled(self) -> bool:
        """Return whether the partition offset is enabled."""
        return bool(self.topic_name)


@dataclass
class KafkaReader(DataReader):
    """Represent an KafkaReader.

    This concrete DataReader reads data from the configured Kafka topic, commits the messages on success and closes the
    consumer on error.
    """

    config: KafkaConsumerConfig = dataclass_field(KafkaConsumerConfig)
    _consumer: KafkaConsumer | None = None
    partition_offset: PartitionOffset = dataclass_field(PartitionOffset)

    @trace_scan(transaction_type=MeasureType.KAFKA.value)
    def read_data(self) -> list[dict]:
        """Read data from the configured indexes."""
        return self._get_data()

    @trace_scan(transaction_type=MeasureType.KAFKA.value)
    def success(self, _: list[dict]) -> None:
        """Commit the offset to Kafka."""
        # Manual recording of consumption offset, needed if enable_auto_commit=False
        self.consumer.commit()

    def error(self, _: list[dict], __: Exception) -> None:
        """Close the consumer to force the creation of a new one for the next run."""
        self.close_consumer()

    def close_consumer(self) -> None:
        """Close the consumer and set the internal attribute to None."""
        self.consumer.close()
        self._consumer = None

    @CaptureSpan(span_type=MeasureType.KAFKA.value)
    def _get_data(self) -> list:
        """Return all available Kafka data corresponding to the current config."""
        parsed_messages: list[dict] = []

        try:
            timeout_seconds = self.config.consumer_timeout / 1000.0
            poll_start = time.time()
            time_elapsed = 0.0

            for _ in range(self.config.max_poll_records):
                time_elapsed = time.time() - poll_start
                if time_elapsed >= timeout_seconds:
                    logging.debug(
                        "Timeout reached while polling Kafka. "
                        f"{time_elapsed=}, {timeout_seconds=}, {len(parsed_messages)=}"
                    )
                    break

                msg: KafkaMessage = self.consumer.poll(timeout=min(1.0, timeout_seconds - time_elapsed))

                if msg is None:
                    continue

                if msg_error := msg.error():
                    if msg_error.code() == KafkaError._PARTITION_EOF:
                        # End of partition event - not an error
                        continue
                    raise OlympusError(f"Error while polling Kafka: {msg_error}")

                msg_value = msg.value()
                if msg_value is None:
                    logging.warning(
                        f"Received message (offset={msg.offset()}) with None value from partition {msg.partition()}"
                    )
                    continue
                message_value = msg_value.decode(self.config.message_encoding)
                parsed_message = json.loads(message_value)
                parsed_messages.append(parsed_message)
                logging.debug(f"data from partition {msg.partition()}: {parsed_message}")

        except Exception as exc:
            logging.warning(f"Failed to poll Kafka: {exc}")
            self.close_consumer()  # Close the consumer to force the creation of a new one for the next run.
            return []

        logging.info(f"Kakfa polled {len(parsed_messages)} messages in {time_elapsed} seconds.")
        return parsed_messages

    @property
    def consumer(self) -> KafkaConsumer:
        """Create a new confluent-kafka Consumer when it doesn't exist.

        The client_id is generated using the following pattern "a2110-olympus-$client_id_suffix".
        The consumer group_id is generated in the following pattern "a2110-olympus-$client_id_suffix-$environment"
        """
        if self._consumer is None:
            client_id_suffix_lower = self.config.client_id_suffix.lower()
            environment_lower = self.config.environment.lower()

            conf = {
                "bootstrap.servers": ",".join(self.config.bootstrap_servers),
                "security.protocol": self.config.security_protocol,
                "sasl.mechanism": self.config.sasl_mechanism,
                "sasl.username": self.config.user,
                "sasl.password": self.config.password,
                "auto.offset.reset": "earliest",  # We choose to use earliest to avoid losing any events.
                "enable.auto.commit": False,  # auto-commit is set to False because we want to wait the success=True.
                "client.id": f"a2110-olympus-{client_id_suffix_lower}",
                "group.id": f"a2110-olympus-{client_id_suffix_lower}-{environment_lower}",
                "max.poll.interval.ms": self.config.max_poll_interval_ms,  # max wait time for a batch of records
            }

            self._consumer = Consumer(conf)

            # Subscribe to topics if not using a specific partition offset
            if not self.partition_offset.is_enabled():
                self._consumer.subscribe(self.config.topics)
            else:
                topic_partition = TopicPartition(
                    self.partition_offset.topic_name,
                    self.partition_offset.partition_number,
                    self.partition_offset.offset,
                )
                self._consumer.assign([topic_partition])

        return self._consumer


TDataReader = TypeVar("TDataReader", bound=DataReader)
