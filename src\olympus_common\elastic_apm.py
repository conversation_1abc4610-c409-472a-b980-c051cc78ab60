"""Module that will contain functions to use the elastic-apm library."""

import inspect
import logging
from functools import wraps
from typing import Any, Callable, TypeVar, cast

import ecs_logging
import elasticapm
from elasticapm.traces import capture_span

from olympus_common.config import ElasticApmConfig
from olympus_common.elastic_apm_logging import LoggingHandler

FuncType = Callable[..., Any]
_AnnotatedFunctionT = TypeVar("_AnnotatedFunctionT", bound=FuncType)


class CaptureSpan(capture_span):
    """Represent the CaptureSpan functionality."""

    def __call__(self, func: _AnnotatedFunctionT) -> _AnnotatedFunctionT:
        """Override the __call__ method to respect enable_elastic_apm setting."""

        @wraps(func)
        def decorated(*args, **kwds):
            if not elastic_apm.config.enable_elastic_apm:
                return func(*args, **kwds)
            return super(CaptureSpan, self).__call__(func)(*args, **kwds)

        return cast(_AnnotatedFunctionT, decorated)


def trace_scan(transaction_type: str, trace_parent: str | None = None, links: list | None = None):
    """Trace functions with elastic-apm to monitor the performance.

    Parameters
    ----------
    transaction_type: str
        This defines the type of transaction (for example: 'request', 'task', 'custom', etc.).
        This is used to categorise the transaction in APM.

    trace_parent: str | None
        This is the header of the parent trace. It allows you to link this transaction to a parent trace.

    links: list | None
        This parameter is used to link the transaction to other transactions or events.
    """

    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            """Async wrapper for coroutine functions."""
            if not elastic_apm.config.enable_elastic_apm:
                return await func(*args, **kwargs)

            elasticapm.instrument()
            transaction = elastic_apm.client.begin_transaction(transaction_type, trace_parent, links)
            elasticapm.set_transaction_name(f"{func.__module__}.{func.__qualname__}")
            try:
                result = await func(*args, **kwargs)
                elastic_apm.client.end_transaction(transaction.name, result="success")
                return result
            except Exception as exc:
                elastic_apm.client.capture_exception()
                elastic_apm.client.end_transaction(transaction.name, result="error")
                raise exc

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            """Sync wrapper for regular functions."""
            if not elastic_apm.config.enable_elastic_apm:
                return func(*args, **kwargs)

            elasticapm.instrument()
            transaction = elastic_apm.client.begin_transaction(transaction_type, trace_parent, links)
            elasticapm.set_transaction_name(f"{func.__module__}.{func.__qualname__}")
            try:
                result = func(*args, **kwargs)
                elastic_apm.client.end_transaction(transaction.name, result="success")
                return result
            except Exception as exc:
                elastic_apm.client.capture_exception()
                elastic_apm.client.end_transaction(transaction.name, result="error")
                raise exc

        # Choose the appropriate wrapper based on the function type
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        return sync_wrapper

    return decorator


class ElasticAPM:
    """Represent the ElasticAPM functionality."""

    def __init__(self, config: ElasticApmConfig | None = None):
        """Initialize the elastic-apm class lazily."""
        self._config = config
        self._client = None

    @property
    def config(self) -> ElasticApmConfig:
        """Return the elastic-apm configuration."""
        if not self._config:
            self._config = ElasticApmConfig()
        return self._config

    @property
    def client(self):
        """Return the elastic-apm client."""
        if not self.config.enable_elastic_apm:
            return None
        if not self._client:
            self._client = elasticapm.Client(config=self.config.client_config)
            formatter = ecs_logging.StdlibFormatter()
            apm_handler = LoggingHandler(client=self._client)
            apm_handler.setFormatter(formatter)
            apm_handler.setLevel(self.config.elastic_log_level)
            logging.getLogger().addHandler(apm_handler)
        return self._client

    def capture_exception(self, exc_info: Any | None = None, handled: bool = True, **kwargs: Any) -> Any | None:
        """Capture the exception.

        This is a wrapper around the elastic-apm client's capture_exception method.
        """
        if not self.client:
            return None
        return self.client.capture_exception(exc_info=exc_info, handled=handled, **kwargs)


elastic_apm = ElasticAPM()
