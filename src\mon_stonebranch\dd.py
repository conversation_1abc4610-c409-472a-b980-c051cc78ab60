"""Detail design implementation for mon-stonebranch."""

import datetime
import json

import pandas as pd
from sqlalchemy.orm import Session

from mon_stonebranch import patterns, statics
from mon_stonebranch.config import config
from mon_stonebranch.utils import convert_hex_to_ip, get_hex_ip_from_message, is_task_object
from olympus_common import db, enums, utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the stonebranch details design script."""
    if config.debug:
        pd.set_option("display.max_columns", None)

    heartbeat_cis = db.AgentHeartbeat.get_heartbeat_cis("Stonebranch", session) or []
    agent_id = db.Agent.get_agent_id_from_name("Stonebranch", session)
    return _transform(df, heartbeat_cis, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable_alarm(row: pd.Series, heartbeat_cis: list[str]) -> bool:
    """Indicate if an alarm is actionable or not."""
    if is_task_object(row):
        # Task events
        if (
            row["node"] not in statics.node_actionable and row["ops_task_name"][1] != "P"
        ) or statics.node_actionable.get(row["node"]) == "No":
            # Events coming from Stonebranch non-prod are non-actionable.
            return False
        elif int(row["ops_task_severity"]) in [3, 4, 5]:
            # Severity minor, major and critical
            return True
        elif int(row["ops_task_severity"]) == 2:
            # Severity warning
            return False
        elif int(row["ops_task_severity"]) == 1:
            # Severity info
            if row["ci_id"] in heartbeat_cis:
                # Heartbeats
                return False
            else:
                # Clears
                return True
        else:
            # Unexpected value
            return False
    else:
        # Connector events
        if int(row["ops_connector_severity"]) in [3, 4, 5]:
            # Severity minor, major and critical
            return True
        elif int(row["ops_connector_severity"]) == 1:
            # Severity info
            return True
        elif int(row["ops_connector_severity"]) == 2:
            # Severity warning
            return False
        else:
            # Unexpected value
            return False


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _delay(row: pd.Series) -> int:
    if is_task_object(row) and int(row["ops_task_severity"]) in [3, 4, 5]:
        return 300
    elif patterns.delay_regex.match(row["summary"]):
        return 900
    else:
        return 0


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _additional_information(row: pd.Series) -> str:
    """Indicate in which platform the server runs. This data is extracted from the event."""
    if is_task_object(row):
        return row["ops_task_exec_id"]
    else:
        return "Not use"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    if is_task_object(row):
        task_name = row["ops_task_name"]
        if task_name == "A2005_C0000_HeartbeatUAC":
            return f"{task_name}_{row['platform']}"

        return task_name
    else:
        txt = row["ops_connector_name"]
        x = txt.split(":")
        return x[0]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type() -> str:
    """Indicate the way the clear is made."""
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _extended_attr(row: pd.Series) -> str:
    """Indicate the extended attributes."""
    return json.dumps({"exec_id": _additional_information(row)})


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Name of the module that handle the DD."""
    return "mon-stonebranch"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type(row: pd.Series, heartbeat_cis: list[str]) -> str:
    """Indicate the alarm metrics."""
    if row["ci_id"] in heartbeat_cis:
        return "/ApplicationEvent/"

    return "/Scheduling/"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series, heartbeat_cis: list[str]) -> str:
    """Indicate the metric name of the alarm."""
    if row["ci_id"] in heartbeat_cis:
        return "Heartbeat"

    return row["ops_task_type"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Indicate the Hostname or the IP value of the Hostname e.g:'bdiraiorlc012'."""
    message = row["message"]
    if not message:
        return "N/A"
    try:
        agent_addr = json.loads(message)["agent_addr"]
    except (json.JSONDecodeError, KeyError):
        # Older messages have a message in a different form
        get_hex_ip = get_hex_ip_from_message(message)
        return convert_hex_to_ip(get_hex_ip)
    return agent_addr


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _handle_time() -> datetime.datetime:
    """Determine the handle time, i.e. the time when the alarm is handled by Olympus."""
    return utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _platform(row: pd.Series) -> str:
    """Indicate in which platform the server runs."""
    return statics.node_platform.get(row["node"], "N/A")


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime.datetime:
    """First date and time when the problem alarm was generated (timestamp sent by EMS).

    Sometimes the timestamp has 9 characters for the milliseconds, other times it has 3. We need to handle both cases.
    """
    suffix = row["timestamp"].split(".")[-1]
    if len(suffix) > 4:  # check gt 4 since Z is also present in the suffix
        _timestamp_str = f"{row['timestamp'][:-4]}Z"
    else:
        _timestamp_str = row["timestamp"]
    return datetime.datetime.strptime(_timestamp_str, "%Y-%m-%dT%H:%M:%S.%fZ")  # Limit to microseconds.


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Represent the MD group that is responsible for handling that alarm."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series, heartbeat_cis: list[str] | None = None) -> str:
    """Represent the alarm summary."""
    if is_task_object(row):
        if heartbeat_cis and int(row["ops_task_severity"]) == 1 and row["ops_task_name"] in heartbeat_cis:
            return "StoneBranch HeartBeat Alarm"
        else:
            if row["ops_task_agent"] != "n/a":
                return f"""{row["ops_task_name"]} type {row["ops_task_type"]}
{row["ops_task_status_code"]} on {row["ops_task_agent"]}"""
            else:
                return f"{row['ops_task_name']} type {row['ops_task_type']} {row['ops_task_status_code']}"
    else:
        return f"{row['ops_connector_name']} type {row['ops_connector_type']} {row['ops_connector_mode']}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Indicate the alarm severity."""
    severity = row["ops_task_severity"]
    return int(severity) if severity != "N/A" and not pd.isna(severity) else 1


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series, heartbeat_cis: list[str]) -> datetime.datetime | None:
    """Indicate the clear time of the alarm."""
    return _raise_time(row) if _type(row, heartbeat_cis) == enums.AlarmType.RESOLUTION.value else None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _type(row: pd.Series, heartbeat_cis: list[str]) -> str:
    """Determine the event type."""
    if is_task_object(row):
        if str(row["ops_task_severity"]) == "1":
            if row["ci_id"] in heartbeat_cis:
                return enums.AlarmType.HEARTBEAT.value
            else:
                return enums.AlarmType.RESOLUTION.value
        else:
            return enums.AlarmType.PROBLEM.value
    else:
        if str(row["ops_task_severity"]) == "1":
            return enums.AlarmType.RESOLUTION.value
        else:
            return enums.AlarmType.PROBLEM.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime.datetime:
    """Return the wake-up time based on the delay."""
    return _raise_time(row) + datetime.timedelta(0, _delay(row))


# ============================
# Main code section
# ============================
@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df_records: pd.DataFrame, heartbeat_cis: list[str], agent_id: int) -> pd.DataFrame:
    # The existence of some columns is conditional to the "tags" column. So we add them back if they don't exist.

    if df_records.empty:
        return df_records

    # Hardcoded fields
    df_records["agent_id"] = agent_id
    df_records["manager"] = _manager()
    df_records["action_class"] = _scope()
    df_records["clear_type"] = _clear_type()
    df_records["handle_time"] = _handle_time()
    df_records["persist_clears_without_alarm"] = False

    # Computed fields
    df_records["node"] = df_records.apply(_node, axis=1)
    df_records["node_alias"] = df_records["node"]
    df_records["platform"] = df_records.apply(_platform, axis=1)
    df_records["ci_id"] = df_records.apply(_ci_id, axis=1)
    df_records["actionable"] = df_records.apply(_actionable_alarm, heartbeat_cis=heartbeat_cis, axis=1)
    df_records["metric_name"] = df_records.apply(_metric_name, heartbeat_cis=heartbeat_cis, axis=1)
    df_records["metric_type"] = df_records.apply(_metric_type, heartbeat_cis=heartbeat_cis, axis=1)
    df_records["additional_data"] = df_records.apply(_extended_attr, axis=1)
    df_records["raise_time"] = df_records.apply(_raise_time, axis=1)
    df_records["summary"] = df_records.apply(_summary, heartbeat_cis=heartbeat_cis, axis=1)
    df_records["severity"] = df_records.apply(_severity, axis=1)
    df_records["clear_time"] = df_records.apply(_clear_time, heartbeat_cis=heartbeat_cis, axis=1)
    df_records["event_type"] = df_records.apply(_type, heartbeat_cis=heartbeat_cis, axis=1)
    df_records["wake_up_time"] = df_records.apply(_wake_up_time, axis=1)

    df_records.sort_values(by=["raise_time"], inplace=True)

    return df_records
