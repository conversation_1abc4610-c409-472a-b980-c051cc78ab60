# Database Configuration for Alembic Migrations
# Copy this file to .env and fill in your actual database credentials

# Database connection settings
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_username
DB_PASSWORD=your_password
DB_SCHEMA=d2110

# Example for local development:
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=olympus_dev
# DB_USER=postgres
# DB_PASSWORD=your_password
# DB_SCHEMA=d2110

# Example for production:
# DB_HOST=your-production-host
# DB_PORT=5432
# DB_NAME=olympus_prod
# DB_USER=olympus_user
# DB_PASSWORD=secure_password
# DB_SCHEMA=d2110
