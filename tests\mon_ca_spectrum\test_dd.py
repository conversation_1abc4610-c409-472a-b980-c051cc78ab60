import pandas as pd

from mon_ca_spectrum.dd import _ci_id, _f5_pool
from mon_ca_spectrum.oid_mapper import OIDMapper


def kafka_message():
    """Return a message that can be used to test _ci_id.

    This message failed to be transformed by the dd at some point, warranting the use of this message for the test.
    """
    return {
        "severity": "MAJOR",
        "mhandle": "0x8765002",
        "secstr": "",
        "clearable": "FALSE",
        "alarm_state": "NEW",
        "event_message": (
            "Wed 27 Dec, 2023 - 06:00:45 - Device asblock01.network.railb.be of type SwCiscoIOS is no longer "
            "responding to primary management requests (e.g. SNMP), but appears to be responsive to other "
            "communication protocol (e.g. ICMP).  This condition has persisted for an extended amount of time.  An "
            "alarm will be generated.   (event [0x00010daa])\n"
        ),
        "ackd": "FALSE",
        "date": "12/27/2023",
        "dtype": "Cisco Catalyst 36xx",
        "alarm_id": "9172795",
        "@version": "1",
        "cause_code": "10701",
        "mname": "asblock01.network.railb.be",
        "repair_person": "",
        "status": "",
        "landscape": "0x8700000",
        "@timestamp": "2023-12-27T05:05:49.018417411Z",
        "server": "iictyiaplv077",
        "time": "06:00:45",
        "mthandle": "0x2100b2",
        "ipaddress": "**********",
        "event_type": "1",
        "mtype": "SwCiscoIOS",
        "event": {
            "logstash": {"instance_name": "iictmiapls016"},
            "kafka": {
                "key": None,
                "consumer_group": "a1559-logstash-a1215-spectrum-events-prd",
                "offset": 1810626,
                "partition": 0,
                "timestamp": "2023-12-27T05:05:48.873Z",
                "topic": "a1215-spectrum-events-prd",
            },
            "uuid": "e53fc114-17d4-4fe2-8a83-0e5cb618e161",
        },
    }


def test_ci_id():
    """Test that the ci_id is correctly extracted from the event_message."""
    oid_mapper = OIDMapper()
    df = pd.DataFrame([kafka_message()])
    df = oid_mapper.transform_df(df)
    df["f5_pool"] = df.apply(_f5_pool, axis=1)  # Required for ci_id
    df["ci_id"] = df.apply(_ci_id, axis=1)
    assert (df["ci_id"] == "asblock01").all()
