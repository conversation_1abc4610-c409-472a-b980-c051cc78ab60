"""ingest module for ing-zabbix. This contains all the logic for the ingestion of the events from the zabbix API."""

from ing_zabbix import utils


def run(events: list[dict], hosts: list[dict]) -> list[dict]:
    """Apply the required transformations to the data received from the zabbix API."""
    explode(events)
    add_os_to_events(events, hosts)

    return events


def add_os_to_events(new_event_list, host_list):
    """Add the os field to the events."""
    if host_list:
        # Manage host field
        for host in host_list:
            utils.explode_host_keys(host)
        # Merging part
        utils.merge_host_event(new_event_list, host_list)


def explode(new_event_list: list[dict]):
    """Apply the required transformations to the events."""
    for event in new_event_list:
        # Explode tags into specific field
        event.update(utils.explode_tags(event["tags"]))

        # Handle the hosts part
        utils.explode_event_keys(event)
