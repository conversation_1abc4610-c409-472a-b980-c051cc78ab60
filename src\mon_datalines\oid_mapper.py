"""OID mapping for Datalines."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Datalines."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "host": "host",
            "@timestamp": "timestamp",
            "snmptrap.ifIndex0": "ifIndex0",
            "snmptrap.ifIndex1": "ifIndex1",
            "snmptrap.tlsCntTreeGlobalIndex": "tlsCntTreeGlobalIndex",
            "snmptrap.tlsTrapDescription": "tlsTrapDescription",
            "snmptrap.tlsSeverityLevel": "tlsSeverityLevel",
            "snmptrap.tlsTrapSeverityLevel": "tlsTrapSeverityLevel",
        }
