{"data": [{"input": {"@timestamp": "2024-07-05T14:18:41.687059575Z", "message": "#<SNMP::SNMPv1_Trap:0x7ed081d7 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x72f741b7 @value=0>, @varbind_list=[#<SNMP::VarBind:0x6fc4db3a @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_DIRECTORY_SERVICES_DOWN\">, #<SNMP::VarBind:0x1560212c @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x5d5230ce @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EDB2F419B53D09C\">, #<SNMP::VarBind:0x7eea7e47 @name=[*******.4.1.694.*******.1.4], @value=\"20240705\">, #<SNMP::VarBind:0x373e67ab @name=[*******.4.1.694.*******.1.5], @value=\"141833\">, #<SNMP::VarBind:0x23c35921 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x6fdbc9d7 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x42ee8a2d @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x6a9aee4 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x3f9ae541 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0xe3e1614 @value=3>>, #<SNMP::VarBind:0x38375a1c @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x73220d70 @value=5>>, #<SNMP::VarBind:0x1bfa4eb8 @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x650a6565 @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209300100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x676f2462 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF92A840D160193\">, #<SNMP::VarBind:0x4c5ec72 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x4d03fddc @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x7b948854 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x65c6ff70 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2115, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T14:18:41.788Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "b229284f-71b1-46f1-b080-8d8b18ae6734", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209300100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EDB2F419B53D09C", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "141833", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF92A840D160193", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240705", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_DIRECTORY_SERVICES_DOWN", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-05 14:23:33", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF92A840D160193", "node_alias": "**************", "raise_time": "2024-07-05 14:18:33", "clear_time": null, "metric_name": "SAP_DIRECTORY_SERVICES_DOWN", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_DIRECTORY_SERVICES_DOWN", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-05T14:18:49.338180290Z", "message": "#<SNMP::SNMPv1_Trap:0xdf73ea4 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x3714a35f @value=0>, @varbind_list=[#<SNMP::VarBind:0x2a1b5097 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_XECM_SERVICES_DOWN\">, #<SNMP::VarBind:0x1ead596e @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0xcb10fbf @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EDB2F419EB4D09C\">, #<SNMP::VarBind:0x2661ab82 @name=[*******.4.1.694.*******.1.4], @value=\"20240705\">, #<SNMP::VarBind:0x568ed831 @name=[*******.4.1.694.*******.1.5], @value=\"141833\">, #<SNMP::VarBind:0x229c9da7 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x26d4a2bc @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0xdc2305f @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x2dad3e86 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x6b1aff31 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x68d871ac @value=3>>, #<SNMP::VarBind:0x7404f420 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x3099bd28 @value=5>>, #<SNMP::VarBind:0xcc62f7d @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x27387a58 @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000210000100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x744e2f6b @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF974FAE50403D4\">, #<SNMP::VarBind:0x9c3bc37 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x34c3c7b5 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x50cd81cf @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x557dc9ca @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictniapls016", "event.kafka.partition": 2, "event.kafka.offset": 1987, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T14:18:49.439Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "3bdc86f3-89d7-42b7-9d69-c25761614289", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000210000100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EDB2F419EB4D09C", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "141833", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF974FAE50403D4", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240705", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_XECM_SERVICES_DOWN", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-05 14:23:33", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF974FAE50403D4", "node_alias": "**************", "raise_time": "2024-07-05 14:18:33", "clear_time": null, "metric_name": "SAP_XECM_SERVICES_DOWN", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_XECM_SERVICES_DOWN", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-05T14:18:43.167040537Z", "message": "#<SNMP::SNMPv1_Trap:0x2a4522a7 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0xe98cf6f @value=0>, @varbind_list=[#<SNMP::VarBind:0x2c7d1aca @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED\">, #<SNMP::VarBind:0x2fae55f8 @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x678514d5 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EDB2F419CFCD09C\">, #<SNMP::VarBind:0x663eee15 @name=[*******.4.1.694.*******.1.4], @value=\"20240705\">, #<SNMP::VarBind:0x22510352 @name=[*******.4.1.694.*******.1.5], @value=\"141833\">, #<SNMP::VarBind:0x7f8d8375 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x30e113ed @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x213e1da2 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0xb5fd144 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x18da7d71 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x209c6bfc @value=3>>, #<SNMP::VarBind:0x711ea140 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x4be9bd6 @value=5>>, #<SNMP::VarBind:0x4fc44fc5 @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0xc441e4a @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209500100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x854ba23 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF9524B0B0883DB\">, #<SNMP::VarBind:0x7844e0d4 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x1513227e @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x3909d8d8 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x14a1e878 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2116, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T14:18:43.271Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "9995c03d-a7d0-425e-88c1-e18a272746d0", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209500100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EDB2F419CFCD09C", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "141833", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF9524B0B0883DB", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240705", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-05 14:23:33", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF9524B0B0883DB", "node_alias": "**************", "raise_time": "2024-07-05 14:18:33", "clear_time": null, "metric_name": "SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-05T14:18:44.750941003Z", "message": "#<SNMP::SNMPv1_Trap:0x528c9c22 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x48882ee4 @value=0>, @varbind_list=[#<SNMP::VarBind:0x47f1eb62 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_QUEUE_JOB_DURATION_EXCEEDED\">, #<SNMP::VarBind:0x712a0232 @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x785cedfd @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EDB2F419D67109C\">, #<SNMP::VarBind:0x55d1592d @name=[*******.4.1.694.*******.1.4], @value=\"20240705\">, #<SNMP::VarBind:0x1db3120e @name=[*******.4.1.694.*******.1.5], @value=\"141833\">, #<SNMP::VarBind:0x3c29b818 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x23859965 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x7cc2b409 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x7964fafd @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x87c74b1 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x73d8a6a @value=3>>, #<SNMP::VarBind:0x6e361478 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x305bb851 @value=5>>, #<SNMP::VarBind:0x5b698b17 @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x30ec34ca @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209600100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x6ea7637b @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF95CCDCCE90419\">, #<SNMP::VarBind:0x353c7ce @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x1475c580 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x45eee9a0 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x1007f6dd @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2117, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T14:18:44.869Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "bce94cc6-1061-4488-bb9c-0b6e9d648bf0", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209600100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EDB2F419D67109C", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "141833", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF95CCDCCE90419", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240705", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_QUEUE_JOB_DURATION_EXCEEDED", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-05 14:23:33", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF95CCDCCE90419", "node_alias": "**************", "raise_time": "2024-07-05 14:18:33", "clear_time": null, "metric_name": "SAP_QUEUE_JOB_DURATION_EXCEEDED", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_QUEUE_JOB_DURATION_EXCEEDED", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-05T14:18:46.244055267Z", "message": "#<SNMP::SNMPv1_Trap:0x4ecb29c6 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x1cf4c0de @value=0>, @varbind_list=[#<SNMP::VarBind:0x1192674c @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_UNIQUE_NAMES_DOWN\">, #<SNMP::VarBind:0x680e9a1 @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x3cc82b9f @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EDB2F419DCD909C\">, #<SNMP::VarBind:0x16f6f5e7 @name=[*******.4.1.694.*******.1.4], @value=\"20240705\">, #<SNMP::VarBind:0x145c4872 @name=[*******.4.1.694.*******.1.5], @value=\"141833\">, #<SNMP::VarBind:0x159076b9 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x68f92347 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x4981ba72 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x3fe874e8 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x29dbe812 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x8991575 @value=3>>, #<SNMP::VarBind:0x4c8f7c3e @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x30bc1e73 @value=5>>, #<SNMP::VarBind:0x46d1eb0e @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x6a36c1c @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209800100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x317a8e7a @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF974D05710C3D4\">, #<SNMP::VarBind:0x616551a0 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x28255f19 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x35fb158d @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x50974416 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2118, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T14:18:46.344Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "d1f48d40-994b-42c0-8806-9024a7ff1f6e", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209800100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EDB2F419DCD909C", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "141833", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF974D05710C3D4", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240705", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_UNIQUE_NAMES_DOWN", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-05 14:23:33", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF974D05710C3D4", "node_alias": "**************", "raise_time": "2024-07-05 14:18:33", "clear_time": null, "metric_name": "SAP_UNIQUE_NAMES_DOWN", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_UNIQUE_NAMES_DOWN", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-05T14:18:47.760425777Z", "message": "#<SNMP::SNMPv1_Trap:0x4b3cf878 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x47b023b3 @value=0>, @varbind_list=[#<SNMP::VarBind:0x2bf120c4 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_WEB_REPORT_CANNOT_BE_EXECUTED\">, #<SNMP::VarBind:0x7a2f2fa @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x4149e406 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EDB2F419E36909C\">, #<SNMP::VarBind:0x36d2c46e @name=[*******.4.1.694.*******.1.4], @value=\"20240705\">, #<SNMP::VarBind:0x14073570 @name=[*******.4.1.694.*******.1.5], @value=\"141833\">, #<SNMP::VarBind:0x4222028 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0xa459d7e @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x2a14d9ec @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x795d56b @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x12a386d1 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x342b5796 @value=3>>, #<SNMP::VarBind:0x38fc2346 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x11eb87bf @value=5>>, #<SNMP::VarBind:0x1ff00e5d @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x71897dd2 @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209900100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x283696b6 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF974E5CADCC3D4\">, #<SNMP::VarBind:0x652d2a70 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x7a30426e @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x40edd717 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x5c727a9a @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2119, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T14:18:47.860Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "1da8d7a8-f3c0-46f6-9ecb-0205087c5817", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209900100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EDB2F419E36909C", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "141833", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF974E5CADCC3D4", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240705", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_WEB_REPORT_CANNOT_BE_EXECUTED", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-05 14:23:33", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF974E5CADCC3D4", "node_alias": "**************", "raise_time": "2024-07-05 14:18:33", "clear_time": null, "metric_name": "SAP_WEB_REPORT_CANNOT_BE_EXECUTED", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_WEB_REPORT_CANNOT_BE_EXECUTED", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-05T14:24:20.986854104Z", "message": "#<SNMP::SNMPv1_Trap:0x5000a5ce @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x285bbe92 @value=0>, @varbind_list=[#<SNMP::VarBind:0x49498d15 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_DIRECTORY_SERVICES_DOWN\">, #<SNMP::VarBind:0x1c326f27 @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x6968fd1d @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EDB2F419B53D09C\">, #<SNMP::VarBind:0x6b455658 @name=[*******.4.1.694.*******.1.4], @value=\"20240705\">, #<SNMP::VarBind:0x6ee15f00 @name=[*******.4.1.694.*******.1.5], @value=\"141833\">, #<SNMP::VarBind:0x61a55eac @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x4f0bd790 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0xebdafbf @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x6a40f837 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x21db9bed @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x30fd6f11 @value=3>>, #<SNMP::VarBind:0x535efee6 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x3eb235fa @value=5>>, #<SNMP::VarBind:0x43cbc532 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF92A840D160193\">, #<SNMP::VarBind:0x1895afbd @name=[*******.4.1.694.*******.1.15], @value=\"C\">, #<SNMP::VarBind:0x3c98db87 @name=[*******.4.1.694.*******.1.16], @value=#<SNMP::Integer:0x132a240b @value=3>>, #<SNMP::VarBind:0xed58109 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x3b083f29 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x41b68f2c @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2120, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T14:24:21.088Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "a13a2d61-9afd-4bb1-b47b-54ef183994f2", "snmptrap.r3maiAlertMetricValue": null, "snmptrap.r3maiAlertStatus": "C", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EDB2F419B53D09C", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "141833", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF92A840D160193", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240705", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": null, "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_DIRECTORY_SERVICES_DOWN", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": "3"}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-05 14:23:33", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF92A840D160193", "node_alias": "**************", "raise_time": "2024-07-05 14:18:33", "clear_time": "2024-01-01 12:00:01", "metric_name": "SAP_DIRECTORY_SERVICES_DOWN", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_DIRECTORY_SERVICES_DOWN", "event_type": "clear", "severity": 0}}, {"input": {"@timestamp": "2024-07-05T19:41:30.958602956Z", "message": "#<SNMP::SNMPv1_Trap:0x5fb0c682 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x2766d462 @value=0>, @varbind_list=[#<SNMP::VarBind:0x2f1107a @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_QUEUE_NEW_ENTRIES_THR_EXCEEDED\">, #<SNMP::VarBind:0x5b2c4270 @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x4a7848b3 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EE0D203B687D0A3\">, #<SNMP::VarBind:0x5d3c8b13 @name=[*******.4.1.694.*******.1.4], @value=\"20240705\">, #<SNMP::VarBind:0x57c4320e @name=[*******.4.1.694.*******.1.5], @value=\"194118\">, #<SNMP::VarBind:0x6ec2d544 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x66f725ff @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x5f2a4e21 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x29885388 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x5118db08 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0xe9fd409 @value=3>>, #<SNMP::VarBind:0x3c9c8bd @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x6836cf5f @value=5>>, #<SNMP::VarBind:0x7f6189ca @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0xfae25b3 @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209700100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x426f98db @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF968FD45B48431\">, #<SNMP::VarBind:0x1c7dfbfe @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x6f211e62 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x115c3d88 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x2f4bd638 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2127, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T19:41:31.059Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "a067d3d7-c90e-4bcc-ba86-6c9290bfeb19", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209700100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EE0D203B687D0A3", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "194118", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF968FD45B48431", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240705", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_QUEUE_NEW_ENTRIES_THR_EXCEEDED", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-05 19:46:18", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF968FD45B48431", "node_alias": "**************", "raise_time": "2024-07-05 19:41:18", "clear_time": null, "metric_name": "SAP_QUEUE_NEW_ENTRIES_THR_EXCEEDED", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_QUEUE_NEW_ENTRIES_THR_EXCEEDED", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-05T19:41:26.064478778Z", "message": "#<SNMP::SNMPv1_Trap:0x31b8281b @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x443d6cfc @value=0>, @varbind_list=[#<SNMP::VarBind:0x1864e42c @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_DIRECTORY_SERVICES_DOWN\">, #<SNMP::VarBind:0x68ecffcb @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x479934a6 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EE0D203B425D0A3\">, #<SNMP::VarBind:0x1ea95fd5 @name=[*******.4.1.694.*******.1.4], @value=\"20240705\">, #<SNMP::VarBind:0x21539da8 @name=[*******.4.1.694.*******.1.5], @value=\"194118\">, #<SNMP::VarBind:0x2ece3cee @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x20fc0195 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x74ffa5a8 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x497cc842 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x160aa4cf @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x563aff32 @value=3>>, #<SNMP::VarBind:0x5f61d2b8 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x70f03fce @value=5>>, #<SNMP::VarBind:0x339fd1c3 @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x325d6bed @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209300100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x63f89b0c @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF92A840D160193\">, #<SNMP::VarBind:0x1287c6f2 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x5fdb0ca8 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x68016bd @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x2187dd9a @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2125, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-05T19:41:26.170Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "61fa2559-9c56-4981-b752-7db77946c346", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209300100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EE0D203B425D0A3", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "194118", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF92A840D160193", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240705", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_DIRECTORY_SERVICES_DOWN", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-05 19:46:18", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF92A840D160193", "node_alias": "**************", "raise_time": "2024-07-05 19:41:18", "clear_time": null, "metric_name": "SAP_DIRECTORY_SERVICES_DOWN", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_DIRECTORY_SERVICES_DOWN", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-06T17:49:40.994758317Z", "message": "#<SNMP::SNMPv1_Trap:0x1dac507c @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x10f287f3 @value=0>, @varbind_list=[#<SNMP::VarBind:0x9d9eb67 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_CONTENT_WS_DOWN\">, #<SNMP::VarBind:0x76897590 @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x72c0a9ad @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EF803B4571FD09C\">, #<SNMP::VarBind:0x675a7782 @name=[*******.4.1.694.*******.1.4], @value=\"20240706\">, #<SNMP::VarBind:0x211e2add @name=[*******.4.1.694.*******.1.5], @value=\"174935\">, #<SNMP::VarBind:0x1c06ee9b @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x6b7cb177 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x5f2e3dfd @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0xf36ac07 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x24eff94c @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x2a9603af @value=3>>, #<SNMP::VarBind:0x4f902d5 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x3e95c0d9 @value=5>>, #<SNMP::VarBind:0x65eb7bde @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x7f7a87b7 @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209200100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x3359cd27 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF91DE5087EC193\">, #<SNMP::VarBind:0x235a409d @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x1d51faa3 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x36bd8181 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x4bf05284 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictniapls016", "event.kafka.partition": 2, "event.kafka.offset": 1994, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-06T17:49:41.095Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "abff8d4c-4b01-4197-9db6-2b805146ef1f", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209200100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EF803B4571FD09C", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "174935", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF91DE5087EC193", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240706", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_CONTENT_WS_DOWN", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-06 17:54:35", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF91DE5087EC193", "node_alias": "**************", "raise_time": "2024-07-06 17:49:35", "clear_time": null, "metric_name": "SAP_CONTENT_WS_DOWN", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_CONTENT_WS_DOWN", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-06T17:54:51.098503359Z", "message": "#<SNMP::SNMPv1_Trap:0x3df6f185 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x1dfe9b87 @value=0>, @varbind_list=[#<SNMP::VarBind:0x1534e4c9 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_QUEUE_NEW_ENTRIES_THR_EXCEEDED\">, #<SNMP::VarBind:0x1fc44533 @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x760fd7ef @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EF803B459FD909C\">, #<SNMP::VarBind:0x1b711fd6 @name=[*******.4.1.694.*******.1.4], @value=\"20240706\">, #<SNMP::VarBind:0x29d1a2dd @name=[*******.4.1.694.*******.1.5], @value=\"174935\">, #<SNMP::VarBind:0x14818709 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x1c399ad9 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0xf25b927 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x2bc62554 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x336746ea @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x6358ed0d @value=3>>, #<SNMP::VarBind:0x41168cc0 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x5b60fd33 @value=5>>, #<SNMP::VarBind:0x52b9b0a3 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF968FD45B48431\">, #<SNMP::VarBind:0x762d1f7c @name=[*******.4.1.694.*******.1.15], @value=\"C\">, #<SNMP::VarBind:0x328edf5 @name=[*******.4.1.694.*******.1.16], @value=#<SNMP::Integer:0x5ffd01c6 @value=3>>, #<SNMP::VarBind:0x40de9e32 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x5e16bfaa @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x1a15b6d8 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictniapls015", "event.kafka.partition": 1, "event.kafka.offset": 2003, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-06T17:54:51.198Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "6c3996e0-96f6-4125-8cc6-4fac69199a61", "snmptrap.r3maiAlertMetricValue": null, "snmptrap.r3maiAlertStatus": "C", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EF803B459FD909C", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "174935", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF968FD45B48431", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240706", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": null, "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_QUEUE_NEW_ENTRIES_THR_EXCEEDED", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": "3"}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-06 17:54:35", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF968FD45B48431", "node_alias": "**************", "raise_time": "2024-07-06 17:49:35", "clear_time": "2024-01-01 12:00:01", "metric_name": "SAP_QUEUE_NEW_ENTRIES_THR_EXCEEDED", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_QUEUE_NEW_ENTRIES_THR_EXCEEDED", "event_type": "clear", "severity": 0}}, {"input": {"@timestamp": "2024-07-07T00:11:42.113695274Z", "message": "#<SNMP::SNMPv1_Trap:0x2d51776d @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x19ca43fb @value=0>, @varbind_list=[#<SNMP::VarBind:0x45de8f70 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_QUEUE_JOB_DURATION_EXCEEDED\">, #<SNMP::VarBind:0x631272b9 @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x288c30bb @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8EFEAF7786F050A1\">, #<SNMP::VarBind:0x8c2a751 @name=[*******.4.1.694.*******.1.4], @value=\"20240707\">, #<SNMP::VarBind:0x39d61c36 @name=[*******.4.1.694.*******.1.5], @value=\"001135\">, #<SNMP::VarBind:0x49a1260f @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0xe1de68 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x1d84190a @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x143a8120 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x546b5cf6 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x5af7da8d @value=3>>, #<SNMP::VarBind:0x6786c490 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x27f56ac1 @value=5>>, #<SNMP::VarBind:0x29771e25 @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x4d309a34 @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209600100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x1c436546 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF95CCDCCE90419\">, #<SNMP::VarBind:0x10eca76d @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x17b1563 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x48d3ce42 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x4fbdfb4b @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictniapls015", "event.kafka.partition": 1, "event.kafka.offset": 2004, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-07T00:11:42.215Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "448484a5-1da7-4897-93a9-fffe10f4d05d", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209600100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8EFEAF7786F050A1", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "001135", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF95CCDCCE90419", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240707", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_QUEUE_JOB_DURATION_EXCEEDED", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-07 00:16:35", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF95CCDCCE90419", "node_alias": "**************", "raise_time": "2024-07-07 00:11:35", "clear_time": null, "metric_name": "SAP_QUEUE_JOB_DURATION_EXCEEDED", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_QUEUE_JOB_DURATION_EXCEEDED", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-07T04:56:48.639939902Z", "message": "#<SNMP::SNMPv1_Trap:0x10ff7000 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x52b5b068 @value=0>, @varbind_list=[#<SNMP::VarBind:0x56601502 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED\">, #<SNMP::VarBind:0x3df410be @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x423dc198 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8F83AA01411650A5\">, #<SNMP::VarBind:0x4b03dfc3 @name=[*******.4.1.694.*******.1.4], @value=\"20240707\">, #<SNMP::VarBind:0x1d9d0ba4 @name=[*******.4.1.694.*******.1.5], @value=\"045635\">, #<SNMP::VarBind:0xf18949 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x2c6ca9ce @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x79449647 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0xcf162e0 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x4609f6 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x492de2e9 @value=3>>, #<SNMP::VarBind:0x1406a325 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x13e72527 @value=5>>, #<SNMP::VarBind:0x235e9714 @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x5c2133c @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209500100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x24fc872a @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF9524B0B0883DB\">, #<SNMP::VarBind:0xe18508f @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x59a9806f @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x4ce72395 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0xce238f4 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictniapls016", "event.kafka.partition": 2, "event.kafka.offset": 2012, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-07T04:56:48.741Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "56efe8a2-7b44-42ef-abf0-85ac90ea33cf", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209500100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8F83AA01411650A5", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "045635", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF9524B0B0883DB", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240707", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-07 05:01:35", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF9524B0B0883DB", "node_alias": "**************", "raise_time": "2024-07-07 04:56:35", "clear_time": null, "metric_name": "SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-07T07:41:18.603386283Z", "message": "#<SNMP::SNMPv1_Trap:0x561d2afa @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x7416d5aa @value=0>, @varbind_list=[#<SNMP::VarBind:0x2419b402 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_CONTENT_WS_DOWN\">, #<SNMP::VarBind:0x1b6c1081 @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x59953dc3 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8F86896AAD5A90A1\">, #<SNMP::VarBind:0x367b43c2 @name=[*******.4.1.694.*******.1.4], @value=\"20240707\">, #<SNMP::VarBind:0x5fab48ab @name=[*******.4.1.694.*******.1.5], @value=\"074111\">, #<SNMP::VarBind:0x24b8a3c7 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x54b3f9b6 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x38bac5b1 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x3dd13c1d @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x5c9616fb @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x17232a96 @value=3>>, #<SNMP::VarBind:0x795ae28e @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x4d5f086c @value=5>>, #<SNMP::VarBind:0x2192eef2 @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x2f6b17cc @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209200100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x18bce3d @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF91DE5087EC193\">, #<SNMP::VarBind:0x3422aa08 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x2f13700 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x755395e5 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x2a764754 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2141, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-07T07:41:18.704Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "6136579a-0aac-4daa-87af-0a42fd2e395c", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209200100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8F86896AAD5A90A1", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "074111", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF91DE5087EC193", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240707", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_CONTENT_WS_DOWN", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-07 07:46:11", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF91DE5087EC193", "node_alias": "**************", "raise_time": "2024-07-07 07:41:11", "clear_time": null, "metric_name": "SAP_CONTENT_WS_DOWN", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_CONTENT_WS_DOWN", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-08T05:43:41.913817493Z", "message": "#<SNMP::SNMPv1_Trap:0x10292067 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x4fd770f5 @value=0>, @varbind_list=[#<SNMP::VarBind:0x17e9a2bd @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_ARCHIVE_SERVER_DOWN\">, #<SNMP::VarBind:0x4e9a823f @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x3abe13c1 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8F9DA0F072B4D0A5\">, #<SNMP::VarBind:0x68988ec8 @name=[*******.4.1.694.*******.1.4], @value=\"20240708\">, #<SNMP::VarBind:0x32be252c @name=[*******.4.1.694.*******.1.5], @value=\"054336\">, #<SNMP::VarBind:0x282fd23e @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x6245ece1 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x4e87c0af @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0xdd31b16 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x5b3b96b0 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x16544bd2 @value=3>>, #<SNMP::VarBind:0x66a6630f @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x73b8df66 @value=5>>, #<SNMP::VarBind:0x743159b7 @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x400f9638 @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209000100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x15642b2d @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF8F285252C43D4\">, #<SNMP::VarBind:0x20128508 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x5e909c5c @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x62927fe0 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x569b1ddc @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2153, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-08T05:43:42.014Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "db53d3df-78f5-494b-9e0f-7fad8afa6145", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209000100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8F9DA0F072B4D0A5", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "054336", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF8F285252C43D4", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240708", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_ARCHIVE_SERVER_DOWN", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-08 05:48:36", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF8F285252C43D4", "node_alias": "**************", "raise_time": "2024-07-08 05:43:36", "clear_time": null, "metric_name": "SAP_ARCHIVE_SERVER_DOWN", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_ARCHIVE_SERVER_DOWN", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-08T05:43:43.446484175Z", "message": "#<SNMP::SNMPv1_Trap:0x6215f8c2 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x38bdfaa0 @value=0>, @varbind_list=[#<SNMP::VarBind:0x47da6e3d @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_CONTENT_SERVER_DOWN\">, #<SNMP::VarBind:0xac44dec @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x5dfbdeee @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8F9DA0F0741190A5\">, #<SNMP::VarBind:0x1b6532c6 @name=[*******.4.1.694.*******.1.4], @value=\"20240708\">, #<SNMP::VarBind:0xfda0c11 @name=[*******.4.1.694.*******.1.5], @value=\"054336\">, #<SNMP::VarBind:0x27599b2b @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x24d62d80 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x1697d8de @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x4972f499 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x2299ae5f @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x37b62da1 @value=3>>, #<SNMP::VarBind:0x1fcf52a1 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x4147bbfd @value=5>>, #<SNMP::VarBind:0x2ec30edc @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x289ef580 @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209100100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x4da1e068 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF9082B96FE43C5\">, #<SNMP::VarBind:0x31453272 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x7c67f68 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x58aba8e5 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x1acbb138 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2154, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-08T05:43:43.546Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "68d7cefa-8a45-48dc-8910-e0a008c9ac68", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209100100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8F9DA0F0741190A5", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "054336", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF9082B96FE43C5", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240708", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_CONTENT_SERVER_DOWN", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-08 05:48:36", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF9082B96FE43C5", "node_alias": "**************", "raise_time": "2024-07-08 05:43:36", "clear_time": null, "metric_name": "SAP_CONTENT_SERVER_DOWN", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_CONTENT_SERVER_DOWN", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-09T10:49:44.377214372Z", "message": "#<SNMP::SNMPv1_Trap:0x5d37bba5 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x7ade93ba @value=0>, @varbind_list=[#<SNMP::VarBind:0x173cdf33 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_QUEUE_JOB_DURATION_EXCEEDED\">, #<SNMP::VarBind:0x77b6a4e2 @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x79fbaaed @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FBC1E5C92A71623\">, #<SNMP::VarBind:0x426a8648 @name=[*******.4.1.694.*******.1.4], @value=\"20240709\">, #<SNMP::VarBind:0x335e0c71 @name=[*******.4.1.694.*******.1.5], @value=\"104937\">, #<SNMP::VarBind:0x710d3f4c @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x55055f42 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x434bdb6c @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x357af0 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x18dac2e5 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x67da61f3 @value=3>>, #<SNMP::VarBind:0x7ad746df @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x511e418f @value=5>>, #<SNMP::VarBind:0x1f9d7ca3 @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x2bfc435a @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209600100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x6b5d1944 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF95CCDCCE90419\">, #<SNMP::VarBind:0x486c3f99 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x3c602a53 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x754f5816 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x765be8c2 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictniapls015", "event.kafka.partition": 1, "event.kafka.offset": 2020, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-09T10:49:44.478Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "109b4055-eb64-465c-9fd5-d567674aa98c", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209600100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FBC1E5C92A71623", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "104937", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF95CCDCCE90419", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240709", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_QUEUE_JOB_DURATION_EXCEEDED", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-09 10:54:37", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF95CCDCCE90419", "node_alias": "**************", "raise_time": "2024-07-09 10:49:37", "clear_time": null, "metric_name": "SAP_QUEUE_JOB_DURATION_EXCEEDED", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_QUEUE_JOB_DURATION_EXCEEDED", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-10T10:33:44.836532143Z", "message": "#<SNMP::SNMPv1_Trap:0xb6988a @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x39cb95ff @value=0>, @varbind_list=[#<SNMP::VarBind:0x5fec68d1 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED\">, #<SNMP::VarBind:0x6b2ad96e @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0xeabcd99 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FD4FC2101D5D623\">, #<SNMP::VarBind:0x21112d77 @name=[*******.4.1.694.*******.1.4], @value=\"20240710\">, #<SNMP::VarBind:0x1d877364 @name=[*******.4.1.694.*******.1.5], @value=\"103338\">, #<SNMP::VarBind:0x20db2813 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x704cee94 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x581e0f7f @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x7ea44b4b @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x7c67f3c @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x10e9091e @value=3>>, #<SNMP::VarBind:0x3824998b @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x640663d7 @value=5>>, #<SNMP::VarBind:0x500dcff4 @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x8f49d12 @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209500100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x1480b546 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF9524B0B0883DB\">, #<SNMP::VarBind:0x43cf64da @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x7d836138 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x51336f97 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x4088ca72 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictniapls015", "event.kafka.partition": 1, "event.kafka.offset": 2025, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-10T10:33:44.946Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "b445b799-8066-4e83-a9d7-d9fcf6a32422", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209500100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FD4FC2101D5D623", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "103338", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF9524B0B0883DB", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240710", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-10 10:38:38", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF9524B0B0883DB", "node_alias": "**************", "raise_time": "2024-07-10 10:33:38", "clear_time": null, "metric_name": "SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-11T04:59:45.186551387Z", "message": "#<SNMP::SNMPv1_Trap:0x704ef84a @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x1bf8b0ab @value=0>, @varbind_list=[#<SNMP::VarBind:0x69527522 @name=[*******.4.1.694.*******.1.1], @value=\"OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED\">, #<SNMP::VarBind:0x6e594cea @name=[*******.4.1.694.*******.1.2], @value=\"BPMON_OBJ\">, #<SNMP::VarBind:0x307ad462 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FE84C654411509D\">, #<SNMP::VarBind:0x641b9fb3 @name=[*******.4.1.694.*******.1.4], @value=\"20240711\">, #<SNMP::VarBind:0x1e9a1d77 @name=[*******.4.1.694.*******.1.5], @value=\"045939\">, #<SNMP::VarBind:0x3a002c52 @name=[*******.4.1.694.*******.1.6], @value=\"Dependent Data Collector for Push WS\">, #<SNMP::VarBind:0x5c3f36b4 @name=[*******.4.1.694.*******.1.7], @value=\"APPMON_NAWSPUSH_ALERT\">, #<SNMP::VarBind:0x64e1cc87 @name=[*******.4.1.694.*******.1.8], @value=\"n/a\">, #<SNMP::VarBind:0x2ade04c9 @name=[*******.4.1.694.*******.1.9], @value=\"EXCEPTION\">, #<SNMP::VarBind:0x1187cf7c @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x3428c16e @value=3>>, #<SNMP::VarBind:0x5e47a698 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x4e519d5 @value=5>>, #<SNMP::VarBind:0x2bb6ca1a @name=[*******.4.1.694.*******.1.12], @value=\"Alert level\">, #<SNMP::VarBind:0x59674db3 @name=[*******.4.1.694.*******.1.13], @value=\"No alert records for YCX0021119027 000000209500100 NAWSPUSH20 001\">, #<SNMP::VarBind:0x3a775361 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDB9AF9524B0B0883DB\">, #<SNMP::VarBind:0x1670ce4b @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0xb8b3085 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x68e2ee92 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x688e54b4 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictniapls016", "event.kafka.partition": 2, "event.kafka.offset": 2029, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T04:59:45.291Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "ffbfda52-9c31-4d68-888b-dd00278f71d5", "snmptrap.r3maiAlertMetricValue": "No alert records for YCX0021119027 000000209500100 NAWSPUSH20 001", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "5", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FE84C654411509D", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "045939", "snmptrap.r3maiAlertMOId": "464BFD013E031EDB9AF9524B0B0883DB", "snmptrap.r3maiAlertCategory": "EXCEPTION", "snmptrap.r3maiAlertTechnicalName": "APPMON_NAWSPUSH_ALERT", "snmptrap.r3maiAlertDate": "20240711", "snmptrap.r3maiAlertMOType": "BPMON_OBJ", "snmptrap.r3maiAlertMetricName": "Alert level", "snmptrap.r3maiAlertDescription": "n/a", "snmptrap.r3maiAlertMOName": "OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "snmptrap.r3maiAlertName": "Dependent Data Collector for Push WS", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": null, "wake_up_time": "2024-07-11 05:04:39", "ci_id": "OTX", "clear_type": "automatic", "event_id": "464BFD013E031EDB9AF9524B0B0883DB", "node_alias": "**************", "raise_time": "2024-07-11 04:59:39", "clear_time": null, "metric_name": "SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "metric_type": "/SAPEvent/", "summary": "OTX_INFRABEL_SAP_QUEUE_ERROR_ENTRIES_THR_EXCEEDED", "event_type": "problem", "severity": 4}}, {"input": {"@timestamp": "2024-07-11T07:42:12.998755093Z", "message": "#<SNMP::SNMPv1_Trap:0x29715ef2 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x27b6b5ac @value=0>, @varbind_list=[#<SNMP::VarBind:0x668bbf97 @name=[*******.4.1.694.*******.1.1], @value=\"SIX~ABAP~sapsix_SIX_52\">, #<SNMP::VarBind:0x452efb7a @name=[*******.4.1.694.*******.1.2], @value=\"INSTANCE\">, #<SNMP::VarBind:0x57a73da6 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FEB228BC0D28019\">, #<SNMP::VarBind:0x3de827f5 @name=[*******.4.1.694.*******.1.4], @value=\"20240711\">, #<SNMP::VarBind:0x6a440d8 @name=[*******.4.1.694.*******.1.5], @value=\"074208\">, #<SNMP::VarBind:0x34638e5d @name=[*******.4.1.694.*******.1.6], @value=\"ABAP Instance not available\">, #<SNMP::VarBind:0x5ac7df7a @name=[*******.4.1.694.*******.1.7], @value=\"ABAP_INSTANCE_AVAILABILITY\">, #<SNMP::VarBind:0x26ec8c58 @name=[*******.4.1.694.*******.1.8], @value=\"The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re\">, #<SNMP::VarBind:0x2eabadd @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x2a53cdbb @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x4e0beb84 @value=3>>, #<SNMP::VarBind:0xc473051 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x5b2c5f56 @value=7>>, #<SNMP::VarBind:0x6f267f9d @name=[*******.4.1.694.*******.1.12], @value=\"Instance Local RFC Availability\">, #<SNMP::VarBind:0x7ee0ee36 @name=[*******.4.1.694.*******.1.13], @value=\"RFC Ping failed\">, #<SNMP::VarBind:0x4b108c03 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDA97C4A9890D298B3F\">, #<SNMP::VarBind:0x72ffacd8 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x2e79ff8d @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x5711252a @value=4>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x182ecb5f @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2174, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T07:42:13.108Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "929bc770-4acb-4b94-a09d-9af855ed6006", "snmptrap.r3maiAlertMetricValue": "RFC Ping failed", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FEB228BC0D28019", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertTime": "074208", "snmptrap.r3maiAlertMOId": "464BFD013E031EDA97C4A9890D298B3F", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertTechnicalName": "ABAP_INSTANCE_AVAILABILITY", "snmptrap.r3maiAlertDate": "20240711", "snmptrap.r3maiAlertMOType": "INSTANCE", "snmptrap.r3maiAlertMetricName": "Instance Local RFC Availability", "snmptrap.r3maiAlertDescription": "The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re", "snmptrap.r3maiAlertMOName": "SIX~ABAP~sapsix_SIX_52", "snmptrap.r3maiAlertName": "ABAP Instance not available", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": "{\"additional_info\": \"ABAP Instance not available.RFC Ping failed\"}", "wake_up_time": "2024-07-11 07:47:08", "ci_id": "SIX", "clear_type": "automatic", "event_id": "464BFD013E031EDA97C4A9890D298B3F", "node_alias": "**************", "raise_time": "2024-07-11 07:42:08", "clear_time": null, "metric_name": "ABAP_INSTANCE_AVAILABILITY", "metric_type": "/SAPEvent/", "summary": "ABAP Instance not available.RFC Ping failed", "event_type": "problem", "severity": 5}}, {"input": {"@timestamp": "2024-07-11T07:58:30.960199821Z", "message": "#<SNMP::SNMPv1_Trap:0x259e68ee @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x2b59822d @value=0>, @varbind_list=[#<SNMP::VarBind:0x6a648816 @name=[*******.4.1.694.*******.1.1], @value=\"SIX~ABAP~sapsix_SIX_52\">, #<SNMP::VarBind:0x3ceb44ac @name=[*******.4.1.694.*******.1.2], @value=\"INSTANCE\">, #<SNMP::VarBind:0x5b44e1f6 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FEB228BC0D28019\">, #<SNMP::VarBind:0x442173f3 @name=[*******.4.1.694.*******.1.4], @value=\"20240711\">, #<SNMP::VarBind:0xa4b7759 @name=[*******.4.1.694.*******.1.5], @value=\"074208\">, #<SNMP::VarBind:0x6ce38c98 @name=[*******.4.1.694.*******.1.6], @value=\"ABAP Instance not available\">, #<SNMP::VarBind:0x10548ba3 @name=[*******.4.1.694.*******.1.7], @value=\"ABAP_INSTANCE_AVAILABILITY\">, #<SNMP::VarBind:0x5f25dcb1 @name=[*******.4.1.694.*******.1.8], @value=\"The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re\">, #<SNMP::VarBind:0xe0589b3 @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x7e7243a7 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x4acfc659 @value=3>>, #<SNMP::VarBind:0x3bfe5be5 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x19941538 @value=7>>, #<SNMP::VarBind:0x759712d6 @name=[*******.4.1.694.*******.1.12], @value=\"Instance Local RFC Availability\">, #<SNMP::VarBind:0x415eff33 @name=[*******.4.1.694.*******.1.13], @value=\"RFC Ping failed\">, #<SNMP::VarBind:0x836b58e @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDA97C4A9890D298B3F\">, #<SNMP::VarBind:0x31324c65 @name=[*******.4.1.694.*******.1.15], @value=\"C\">, #<SNMP::VarBind:0x7c7ef937 @name=[*******.4.1.694.*******.1.16], @value=#<SNMP::Integer:0x4aef26ec @value=5>>, #<SNMP::VarBind:0x774860fa @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x543a4b12 @value=4>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x5f86a7e3 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2177, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T07:58:31.067Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "6a4b04fd-6300-414a-b334-9b982d06a46e", "snmptrap.r3maiAlertMetricValue": "RFC Ping failed", "snmptrap.r3maiAlertStatus": "C", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FEB228BC0D28019", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertTime": "074208", "snmptrap.r3maiAlertMOId": "464BFD013E031EDA97C4A9890D298B3F", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertTechnicalName": "ABAP_INSTANCE_AVAILABILITY", "snmptrap.r3maiAlertDate": "20240711", "snmptrap.r3maiAlertMOType": "INSTANCE", "snmptrap.r3maiAlertMetricName": "Instance Local RFC Availability", "snmptrap.r3maiAlertDescription": "The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re", "snmptrap.r3maiAlertMOName": "SIX~ABAP~sapsix_SIX_52", "snmptrap.r3maiAlertName": "ABAP Instance not available", "snmptrap.r3maiAlertReasonClosure": "5"}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": "{\"additional_info\": \"ABAP Instance not available.RFC Ping failed\"}", "wake_up_time": "2024-07-11 07:47:08", "ci_id": "SIX", "clear_type": "automatic", "event_id": "464BFD013E031EDA97C4A9890D298B3F", "node_alias": "**************", "raise_time": "2024-07-11 07:42:08", "clear_time": "2024-01-01 12:00:01", "metric_name": "ABAP_INSTANCE_AVAILABILITY", "metric_type": "/SAPEvent/", "summary": "ABAP Instance not available.RFC Ping failed", "event_type": "clear", "severity": 0}}, {"input": {"@timestamp": "2024-07-11T07:43:43.853677450Z", "message": "#<SNMP::SNMPv1_Trap:0x17b83dee @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x765c2d03 @value=0>, @varbind_list=[#<SNMP::VarBind:0x46a03cec @name=[*******.4.1.694.*******.1.1], @value=\"SIXVIRDB\">, #<SNMP::VarBind:0x10213e9b @name=[*******.4.1.694.*******.1.2], @value=\"DBMS\">, #<SNMP::VarBind:0x7823da9 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FEB2962AAFA509C\">, #<SNMP::VarBind:0x7c373518 @name=[*******.4.1.694.*******.1.4], @value=\"20240711\">, #<SNMP::VarBind:0x3bb7312e @name=[*******.4.1.694.*******.1.5], @value=\"074339\">, #<SNMP::VarBind:0x227b0525 @name=[*******.4.1.694.*******.1.6], @value=\"Database Unavailable\">, #<SNMP::VarBind:0x34821bb6 @name=[*******.4.1.694.*******.1.7], @value=\"HDB_DATABASE_AVAILABILITY\">, #<SNMP::VarBind:0x715867cb @name=[*******.4.1.694.*******.1.8], @value=\"The availability checks for database SIXVIRDB have failed. This means that the database could not be connected from Solution Man\">, #<SNMP::VarBind:0x73e4068f @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x94c77fb @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x2db792cf @value=3>>, #<SNMP::VarBind:0x1fe3ce8d @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x4c606380 @value=9>>, #<SNMP::VarBind:0x26793658 @name=[*******.4.1.694.*******.1.12], @value=\"DBA Cockpit Connection Status\">, #<SNMP::VarBind:0x37c9e9b2 @name=[*******.4.1.694.*******.1.13], @value=\"Failure\">, #<SNMP::VarBind:0x365fde60 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDA97C4AA774B84CF89\">, #<SNMP::VarBind:0x796cc748 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x16c8f5cb @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x6e340ad9 @value=4>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x265075a1 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2175, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T07:43:43.954Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "e64303ce-bca4-4417-9f83-5e9477f9b792", "snmptrap.r3maiAlertMetricValue": "Failure", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "9", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FEB2962AAFA509C", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertTime": "074339", "snmptrap.r3maiAlertMOId": "464BFD013E031EDA97C4AA774B84CF89", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertTechnicalName": "HDB_DATABASE_AVAILABILITY", "snmptrap.r3maiAlertDate": "20240711", "snmptrap.r3maiAlertMOType": "DBMS", "snmptrap.r3maiAlertMetricName": "DBA Cockpit Connection Status", "snmptrap.r3maiAlertDescription": "The availability checks for database SIXVIRDB have failed. This means that the database could not be connected from Solution Man", "snmptrap.r3maiAlertMOName": "SIXVIRDB", "snmptrap.r3maiAlertName": "Database Unavailable", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": "{\"additional_info\": \"Database Unavailable.Failure\"}", "wake_up_time": "2024-07-11 07:48:39", "ci_id": "SIX", "clear_type": "automatic", "event_id": "464BFD013E031EDA97C4AA774B84CF89", "node_alias": "**************", "raise_time": "2024-07-11 07:43:39", "clear_time": null, "metric_name": "HDB_DATABASE_AVAILABILITY", "metric_type": "/SAPEvent/", "summary": "Database Unavailable.Failure", "event_type": "problem", "severity": 5}}, {"input": {"@timestamp": "2024-07-11T07:58:24.630615484Z", "message": "#<SNMP::SNMPv1_Trap:0x25dbe9e8 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x2b1c0323 @value=0>, @varbind_list=[#<SNMP::VarBind:0x6a210918 @name=[*******.4.1.694.*******.1.1], @value=\"SIX~ABAP~sapsix_SIX_52\">, #<SNMP::VarBind:0x10cf391d @name=[*******.4.1.694.*******.1.2], @value=\"INSTANCE\">, #<SNMP::VarBind:0x5b01253c @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FEB6AFB6862509D\">, #<SNMP::VarBind:0x68054b86 @name=[*******.4.1.694.*******.1.4], @value=\"20240711\">, #<SNMP::VarBind:0xa0ef657 @name=[*******.4.1.694.*******.1.5], @value=\"075820\">, #<SNMP::VarBind:0x636031da @name=[*******.4.1.694.*******.1.6], @value=\"ABAP Instance not available\">, #<SNMP::VarBind:0x319ac04f @name=[*******.4.1.694.*******.1.7], @value=\"ABAP_INSTANCE_AVAILABILITY\">, #<SNMP::VarBind:0x7301e481 @name=[*******.4.1.694.*******.1.8], @value=\"The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re\">, #<SNMP::VarBind:0xe4008f8 @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x6c4c060b @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0xb333b75 @value=2>>, #<SNMP::VarBind:0x77eaf37e @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x79e322b3 @value=7>>, #<SNMP::VarBind:0x75131fda @name=[*******.4.1.694.*******.1.12], @value=\"Instance Status\">, #<SNMP::VarBind:0x67696a9e @name=[*******.4.1.694.*******.1.13], @value=\"DB connect without progress\">, #<SNMP::VarBind:0x47b8040e @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDA97C4A9890D298B3F\">, #<SNMP::VarBind:0x27ae40f0 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x4aae7610 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x663fd841 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x612028fc @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2176, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T07:58:24.734Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "53dd37aa-253b-4cf3-b2da-2f6e3e91dfea", "snmptrap.r3maiAlertMetricValue": "DB connect without progress", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "2", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FEB6AFB6862509D", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "075820", "snmptrap.r3maiAlertMOId": "464BFD013E031EDA97C4A9890D298B3F", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertTechnicalName": "ABAP_INSTANCE_AVAILABILITY", "snmptrap.r3maiAlertDate": "20240711", "snmptrap.r3maiAlertMOType": "INSTANCE", "snmptrap.r3maiAlertMetricName": "Instance Status", "snmptrap.r3maiAlertDescription": "The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re", "snmptrap.r3maiAlertMOName": "SIX~ABAP~sapsix_SIX_52", "snmptrap.r3maiAlertName": "ABAP Instance not available", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": "{\"additional_info\": \"ABAP Instance not available.DB connect without progress\"}", "wake_up_time": "2024-07-11 08:03:20", "ci_id": "SIX", "clear_type": "automatic", "event_id": "464BFD013E031EDA97C4A9890D298B3F", "node_alias": "**************", "raise_time": "2024-07-11 07:58:20", "clear_time": null, "metric_name": "ABAP_INSTANCE_AVAILABILITY", "metric_type": "/SAPEvent/", "summary": "ABAP Instance not available.DB connect without progress", "event_type": "problem", "severity": 5}}, {"input": {"@timestamp": "2024-07-11T08:09:22.379157329Z", "message": "#<SNMP::SNMPv1_Trap:0xa3f5e9a @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x69ea308e @value=0>, @varbind_list=[#<SNMP::VarBind:0x4ed443b8 @name=[*******.4.1.694.*******.1.1], @value=\"SIX~ABAP~sapsix_SIX_52\">, #<SNMP::VarBind:0x3e6f1f76 @name=[*******.4.1.694.*******.1.2], @value=\"INSTANCE\">, #<SNMP::VarBind:0x6b92675 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FEB9BEFADDA8C8A\">, #<SNMP::VarBind:0x54e02c27 @name=[*******.4.1.694.*******.1.4], @value=\"20240711\">, #<SNMP::VarBind:0x38457c80 @name=[*******.4.1.694.*******.1.5], @value=\"080917\">, #<SNMP::VarBind:0x7e175051 @name=[*******.4.1.694.*******.1.6], @value=\"ABAP Instance not available\">, #<SNMP::VarBind:0x31525b68 @name=[*******.4.1.694.*******.1.7], @value=\"ABAP_INSTANCE_AVAILABILITY\">, #<SNMP::VarBind:0x6406d871 @name=[*******.4.1.694.*******.1.8], @value=\"The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re\">, #<SNMP::VarBind:0x7734090d @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x33655a62 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x10cfce7d @value=2>>, #<SNMP::VarBind:0x42585945 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x15734c62 @value=7>>, #<SNMP::VarBind:0xcdcb2e4 @name=[*******.4.1.694.*******.1.12], @value=\"Instance Status\">, #<SNMP::VarBind:0x620e2924 @name=[*******.4.1.694.*******.1.13], @value=\"Request handling without progress\">, #<SNMP::VarBind:0x621cc651 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDA97C4A9890D298B3F\">, #<SNMP::VarBind:0x6d83962c @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x42142ea @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x17751c43 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x135f6eb2 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2179, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T08:09:22.480Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "071cde7b-03e7-4eec-975e-6a372f679b69", "snmptrap.r3maiAlertMetricValue": "Request handling without progress", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "2", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FEB9BEFADDA8C8A", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "080917", "snmptrap.r3maiAlertMOId": "464BFD013E031EDA97C4A9890D298B3F", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertTechnicalName": "ABAP_INSTANCE_AVAILABILITY", "snmptrap.r3maiAlertDate": "20240711", "snmptrap.r3maiAlertMOType": "INSTANCE", "snmptrap.r3maiAlertMetricName": "Instance Status", "snmptrap.r3maiAlertDescription": "The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re", "snmptrap.r3maiAlertMOName": "SIX~ABAP~sapsix_SIX_52", "snmptrap.r3maiAlertName": "ABAP Instance not available", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": "{\"additional_info\": \"ABAP Instance not available.Request handling without progress\"}", "wake_up_time": "2024-07-11 08:14:17", "ci_id": "SIX", "clear_type": "automatic", "event_id": "464BFD013E031EDA97C4A9890D298B3F", "node_alias": "**************", "raise_time": "2024-07-11 08:09:17", "clear_time": null, "metric_name": "ABAP_INSTANCE_AVAILABILITY", "metric_type": "/SAPEvent/", "summary": "ABAP Instance not available.Request handling without progress", "event_type": "problem", "severity": 5}}, {"input": {"@timestamp": "2024-07-11T08:14:22.164504152Z", "message": "#<SNMP::SNMPv1_Trap:0x345c4de8 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0xd7bb385 @value=0>, @varbind_list=[#<SNMP::VarBind:0x7599323c @name=[*******.4.1.694.*******.1.1], @value=\"SIX~ABAP~sapsix_SIX_52\">, #<SNMP::VarBind:0x23fc126a @name=[*******.4.1.694.*******.1.2], @value=\"INSTANCE\">, #<SNMP::VarBind:0x734b7a0b @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FEBB24625058019\">, #<SNMP::VarBind:0x12c9d08e @name=[*******.4.1.694.*******.1.4], @value=\"20240711\">, #<SNMP::VarBind:0x2efd1839 @name=[*******.4.1.694.*******.1.5], @value=\"081417\">, #<SNMP::VarBind:0x6dd120ce @name=[*******.4.1.694.*******.1.6], @value=\"ABAP Instance not available\">, #<SNMP::VarBind:0x45e2845d @name=[*******.4.1.694.*******.1.7], @value=\"ABAP_INSTANCE_AVAILABILITY\">, #<SNMP::VarBind:0x197361cf @name=[*******.4.1.694.*******.1.8], @value=\"The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re\">, #<SNMP::VarBind:0x5f099e08 @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x3bba0277 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x6a2bb931 @value=2>>, #<SNMP::VarBind:0x6854c0a2 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x7b3efa7e @value=7>>, #<SNMP::VarBind:0x10fce6d4 @name=[*******.4.1.694.*******.1.12], @value=\"Instance Status\">, #<SNMP::VarBind:0x27a937cb @name=[*******.4.1.694.*******.1.13], @value=\"Request handling without progress\">, #<SNMP::VarBind:0x69b6a24c @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDA97C4A9890D298B3F\">, #<SNMP::VarBind:0x65d7580e @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x12ead826 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x7c13b2df @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x20ebdbd3 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2181, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T08:14:22.270Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "103509aa-a872-42f8-a8e5-ab46da320935", "snmptrap.r3maiAlertMetricValue": "Request handling without progress", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "2", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FEBB24625058019", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "081417", "snmptrap.r3maiAlertMOId": "464BFD013E031EDA97C4A9890D298B3F", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertTechnicalName": "ABAP_INSTANCE_AVAILABILITY", "snmptrap.r3maiAlertDate": "20240711", "snmptrap.r3maiAlertMOType": "INSTANCE", "snmptrap.r3maiAlertMetricName": "Instance Status", "snmptrap.r3maiAlertDescription": "The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re", "snmptrap.r3maiAlertMOName": "SIX~ABAP~sapsix_SIX_52", "snmptrap.r3maiAlertName": "ABAP Instance not available", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": "{\"additional_info\": \"ABAP Instance not available.Request handling without progress\"}", "wake_up_time": "2024-07-11 08:19:17", "ci_id": "SIX", "clear_type": "automatic", "event_id": "464BFD013E031EDA97C4A9890D298B3F", "node_alias": "**************", "raise_time": "2024-07-11 08:14:17", "clear_time": null, "metric_name": "ABAP_INSTANCE_AVAILABILITY", "metric_type": "/SAPEvent/", "summary": "ABAP Instance not available.Request handling without progress", "event_type": "problem", "severity": 5}}, {"input": {"@timestamp": "2024-07-11T08:17:38.426558817Z", "message": "#<SNMP::SNMPv1_Trap:0x621cb803 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x6ca480b8 @value=0>, @varbind_list=[#<SNMP::VarBind:0x3b4ec046 @name=[*******.4.1.694.*******.1.1], @value=\"SIX~ABAP~sapsix_SIX_52\">, #<SNMP::VarBind:0x3c95bb8c @name=[*******.4.1.694.*******.1.2], @value=\"INSTANCE\">, #<SNMP::VarBind:0x747c5de6 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FEBC0E773FA109F\">, #<SNMP::VarBind:0x47b0ea44 @name=[*******.4.1.694.*******.1.4], @value=\"20240711\">, #<SNMP::VarBind:0xab15e04 @name=[*******.4.1.694.*******.1.5], @value=\"081733\">, #<SNMP::VarBind:0x1b69cd05 @name=[*******.4.1.694.*******.1.6], @value=\"ABAP Instance not available\">, #<SNMP::VarBind:0xd6c20e3 @name=[*******.4.1.694.*******.1.7], @value=\"ABAP_INSTANCE_AVAILABILITY\">, #<SNMP::VarBind:0x4d4e09c0 @name=[*******.4.1.694.*******.1.8], @value=\"The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re\">, #<SNMP::VarBind:0x4d8f2f13 @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x185bc242 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x745c8292 @value=2>>, #<SNMP::VarBind:0x49e195d0 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x7d22dc48 @value=7>>, #<SNMP::VarBind:0x3b2d487c @name=[*******.4.1.694.*******.1.12], @value=\"Instance Status\">, #<SNMP::VarBind:0x2b751599 @name=[*******.4.1.694.*******.1.13], @value=\"Request handling without progress\">, #<SNMP::VarBind:0x6e7f4a32 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDA97C4A9890D298B3F\">, #<SNMP::VarBind:0x5d41b29 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x54ebdd47 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x57b1d39e @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x430b3d80 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictmiapls016", "event.kafka.partition": 0, "event.kafka.offset": 2183, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T08:17:38.530Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "e03ce628-dc38-47f4-b898-0b8b0a364b3e", "snmptrap.r3maiAlertMetricValue": "Request handling without progress", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "2", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FEBC0E773FA109F", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "081733", "snmptrap.r3maiAlertMOId": "464BFD013E031EDA97C4A9890D298B3F", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertTechnicalName": "ABAP_INSTANCE_AVAILABILITY", "snmptrap.r3maiAlertDate": "20240711", "snmptrap.r3maiAlertMOType": "INSTANCE", "snmptrap.r3maiAlertMetricName": "Instance Status", "snmptrap.r3maiAlertDescription": "The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re", "snmptrap.r3maiAlertMOName": "SIX~ABAP~sapsix_SIX_52", "snmptrap.r3maiAlertName": "ABAP Instance not available", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": "{\"additional_info\": \"ABAP Instance not available.Request handling without progress\"}", "wake_up_time": "2024-07-11 08:22:33", "ci_id": "SIX", "clear_type": "automatic", "event_id": "464BFD013E031EDA97C4A9890D298B3F", "node_alias": "**************", "raise_time": "2024-07-11 08:17:33", "clear_time": null, "metric_name": "ABAP_INSTANCE_AVAILABILITY", "metric_type": "/SAPEvent/", "summary": "ABAP Instance not available.Request handling without progress", "event_type": "problem", "severity": 5}}, {"input": {"@timestamp": "2024-07-11T08:19:24.734307680Z", "message": "#<SNMP::SNMPv1_Trap:0x66a36b9b @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x6ca893ed @value=0>, @varbind_list=[#<SNMP::VarBind:0x2f214cc3 @name=[*******.4.1.694.*******.1.1], @value=\"SIX~ABAP~sapsix_SIX_52\">, #<SNMP::VarBind:0x5223150f @name=[*******.4.1.694.*******.1.2], @value=\"INSTANCE\">, #<SNMP::VarBind:0x7abcd35d @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FEBC8DDAD25D0A7\">, #<SNMP::VarBind:0x2b37be67 @name=[*******.4.1.694.*******.1.4], @value=\"20240711\">, #<SNMP::VarBind:0xed5b896 @name=[*******.4.1.694.*******.1.5], @value=\"081920\">, #<SNMP::VarBind:0x44145f2e @name=[*******.4.1.694.*******.1.6], @value=\"ABAP Instance not available\">, #<SNMP::VarBind:0x582e4aca @name=[*******.4.1.694.*******.1.7], @value=\"ABAP_INSTANCE_AVAILABILITY\">, #<SNMP::VarBind:0x4efc032e @name=[*******.4.1.694.*******.1.8], @value=\"The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re\">, #<SNMP::VarBind:0x6d4e136f @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0xb45ccc7 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x2117af7f @value=2>>, #<SNMP::VarBind:0xf5c3d00 @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x121492ef @value=7>>, #<SNMP::VarBind:0x3712479e @name=[*******.4.1.694.*******.1.12], @value=\"Instance Status\">, #<SNMP::VarBind:0x2be27a54 @name=[*******.4.1.694.*******.1.13], @value=\"Request handling without progress\">, #<SNMP::VarBind:0x4538e615 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDA97C4A9890D298B3F\">, #<SNMP::VarBind:0x730d97b7 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0x568600ff @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x6e0ef0a1 @value=3>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x6c04bb3b @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictniapls015", "event.kafka.partition": 1, "event.kafka.offset": 2026, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T08:19:24.835Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "23f25fce-0cc1-4e55-9589-b3848d97853b", "snmptrap.r3maiAlertMetricValue": "Request handling without progress", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "2", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FEBC8DDAD25D0A7", "snmptrap.r3maiAlertPriority": "3", "snmptrap.r3maiAlertTime": "081920", "snmptrap.r3maiAlertMOId": "464BFD013E031EDA97C4A9890D298B3F", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertTechnicalName": "ABAP_INSTANCE_AVAILABILITY", "snmptrap.r3maiAlertDate": "20240711", "snmptrap.r3maiAlertMOType": "INSTANCE", "snmptrap.r3maiAlertMetricName": "Instance Status", "snmptrap.r3maiAlertDescription": "The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re", "snmptrap.r3maiAlertMOName": "SIX~ABAP~sapsix_SIX_52", "snmptrap.r3maiAlertName": "ABAP Instance not available", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": "{\"additional_info\": \"ABAP Instance not available.Request handling without progress\"}", "wake_up_time": "2024-07-11 08:24:20", "ci_id": "SIX", "clear_type": "automatic", "event_id": "464BFD013E031EDA97C4A9890D298B3F", "node_alias": "**************", "raise_time": "2024-07-11 08:19:20", "clear_time": null, "metric_name": "ABAP_INSTANCE_AVAILABILITY", "metric_type": "/SAPEvent/", "summary": "ABAP Instance not available.Request handling without progress", "event_type": "problem", "severity": 5}}, {"input": {"@timestamp": "2024-07-11T08:26:12.124421643Z", "message": "#<SNMP::SNMPv1_Trap:0x7842e79 @enterprise=[*******.4.1.694.*******.60], @timestamp=#<SNMP::TimeTicks:0x943c0f9 @value=0>, @varbind_list=[#<SNMP::VarBind:0x487ecac2 @name=[*******.4.1.694.*******.1.1], @value=\"SIX~ABAP~sapsix_SIX_52\">, #<SNMP::VarBind:0x6e2fd252 @name=[*******.4.1.694.*******.1.2], @value=\"INSTANCE\">, #<SNMP::VarBind:0x797c8366 @name=[*******.4.1.694.*******.1.3], @value=\"464BFD013E031EDF8FEBE73E6F05509F\">, #<SNMP::VarBind:0x16c7c54d @name=[*******.4.1.694.*******.1.4], @value=\"20240711\">, #<SNMP::VarBind:0x2851358d @name=[*******.4.1.694.*******.1.5], @value=\"082606\">, #<SNMP::VarBind:0x387cdf97 @name=[*******.4.1.694.*******.1.6], @value=\"ABAP Instance not available\">, #<SNMP::VarBind:0x25060c4d @name=[*******.4.1.694.*******.1.7], @value=\"ABAP_INSTANCE_AVAILABILITY\">, #<SNMP::VarBind:0xdc3480d @name=[*******.4.1.694.*******.1.8], @value=\"The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re\">, #<SNMP::VarBind:0x2c1fe965 @name=[*******.4.1.694.*******.1.9], @value=\"AVAIL\">, #<SNMP::VarBind:0x72410b16 @name=[*******.4.1.694.*******.1.10], @value=#<SNMP::Integer:0x67301577 @value=3>>, #<SNMP::VarBind:0x70a4aabc @name=[*******.4.1.694.*******.1.11], @value=#<SNMP::Integer:0x2323210c @value=7>>, #<SNMP::VarBind:0x59c087c5 @name=[*******.4.1.694.*******.1.12], @value=\"Instance Local RFC Availability\">, #<SNMP::VarBind:0x3e3caa27 @name=[*******.4.1.694.*******.1.13], @value=\"RFC Ping failed\">, #<SNMP::VarBind:0x6b180616 @name=[*******.4.1.694.*******.1.14], @value=\"464BFD013E031EDA97C4A9890D298B3F\">, #<SNMP::VarBind:0x51096138 @name=[*******.4.1.694.*******.1.15], @value=\"O\">, #<SNMP::VarBind:0xc2a3a73 @name=[*******.4.1.694.*******.1.17], @value=#<SNMP::Integer:0x571cd443 @value=4>>], @specific_trap=60, @source_ip=\"**************\", @agent_addr=#<SNMP::IpAddress:0x7c9f5000 @value=\"\\n\\xF8\\x16\\x0F\">, @generic_trap=6>", "@version": "1", "type": "snmp_trap", "host": "**************", "event.logstash.instance_name": "iictniapls015", "event.kafka.partition": 1, "event.kafka.offset": 2027, "event.kafka.key": null, "event.kafka.timestamp": "2024-07-11T08:26:12.226Z", "event.kafka.topic": "a1846-sap_solman-events-acc", "event.kafka.consumer_group": "a1559-logstash-a1846-sap_solman-events-acc", "event.uuid": "270e41c0-e997-4e46-9d60-68a098009faa", "snmptrap.r3maiAlertMetricValue": "RFC Ping failed", "snmptrap.r3maiAlertStatus": "O", "snmptrap.r3maiAlertRating": "3", "snmptrap.r3maiAlertSeverity": "7", "snmptrap.r3maiAlertId": "464BFD013E031EDF8FEBE73E6F05509F", "snmptrap.r3maiAlertPriority": "4", "snmptrap.r3maiAlertTime": "082606", "snmptrap.r3maiAlertMOId": "464BFD013E031EDA97C4A9890D298B3F", "snmptrap.r3maiAlertCategory": "AVAIL", "snmptrap.r3maiAlertTechnicalName": "ABAP_INSTANCE_AVAILABILITY", "snmptrap.r3maiAlertDate": "20240711", "snmptrap.r3maiAlertMOType": "INSTANCE", "snmptrap.r3maiAlertMetricName": "Instance Local RFC Availability", "snmptrap.r3maiAlertDescription": "The ABAP instance sapsix_SIX_52 or parts of it are not running. Please check the alert details to see which components do not re", "snmptrap.r3maiAlertMOName": "SIX~ABAP~sapsix_SIX_52", "snmptrap.r3maiAlertName": "ABAP Instance not available", "snmptrap.r3maiAlertReasonClosure": null}, "output": {"agent_id": 0, "manager": "mon-sap-solman", "node": "SolMan", "action_class": "IT", "top_level": null, "handle_time": "2024-01-01 12:00:01", "actionable": null, "additional_data": "{\"additional_info\": \"ABAP Instance not available.RFC Ping failed\"}", "wake_up_time": "2024-07-11 08:31:06", "ci_id": "SIX", "clear_type": "automatic", "event_id": "464BFD013E031EDA97C4A9890D298B3F", "node_alias": "**************", "raise_time": "2024-07-11 08:26:06", "clear_time": null, "metric_name": "ABAP_INSTANCE_AVAILABILITY", "metric_type": "/SAPEvent/", "summary": "ABAP Instance not available.RFC Ping failed", "event_type": "problem", "severity": 5}}]}