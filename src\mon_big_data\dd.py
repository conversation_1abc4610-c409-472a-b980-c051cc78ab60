"""Detail design implementation for mon-big-data."""

import datetime
import json

import pandas as pd
from dateutil.parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from mon_big_data.config import config
from olympus_common import db, enums, utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the big data details design script."""
    if config.debug:
        pd.set_option("display.max_columns", None)

    agent_id = db.Agent.get_agent_id_from_name("BigData", session)

    return _transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _additional_data(row: pd.Series) -> str | None:
    """Copy the request body data into the additional data."""
    if _is_heartbeat_message(row):
        return None

    return json.dumps(
        {
            "a_code": row["a_code"],
            "alert_name": row["alert_name"],
            "ci": row["ci"],
            "metric_name": row["metric_name"],
            "metric_type": row["metric_type"],
            "severity": row["severity"],
            "status": row["status"],
            "summary": row["summary"],
        }
    )


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _is_heartbeat_message(row: pd.Series) -> bool:
    """Determine if the message is a heartbeat."""
    return row["alert_name"] == "prometheus_healthcheck"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable(row: pd.Series) -> bool | None:
    """Create the actionable alarm field for the event."""
    if _is_heartbeat_message(row):
        return False
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    return row["ci"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type() -> str:
    """Indicate the way the clear is made."""
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Name of the module that handle the DD."""
    return "mon-big-data"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type(row: pd.Series) -> str:
    """Indicate the alarm metrics."""
    if _is_heartbeat_message(row):
        return "/ApplicationEvent/"

    return row["metric_type"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str:
    """Determine the metric name."""
    if _is_heartbeat_message(row):
        return "Heartbeat"

    return row["metric_name"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str | None:
    """Determine the node."""
    if row["node"]:
        return row["node"].split(".")[0]

    return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node_alias(row: pd.Series) -> str | None:
    """Determine the node alias."""
    if row["node"]:
        return row["node"].split(".")[0]

    return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime.datetime:
    """First date and time when the problem alarm was generated (timestamp sent by EMS)."""
    # Big data cannot send a raise time for each occurrence. They can only send the first raise time
    # of the alarm.
    return utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _scope() -> str:
    """Represent the MD group that is responsible for handling that alarm."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    if _is_heartbeat_message(row):
        return enums.Severity.INDETERMINATE.value
    elif row["status"] == "resolved":
        return enums.Severity.CLEARED.value
    else:
        match row["severity"]:
            case "info":
                return enums.Severity.INDETERMINATE.value
            case "warning":
                return enums.Severity.WARNING.value
            case "critical":
                return enums.Severity.CRITICAL.value
            case _:
                return enums.Severity.MAJOR.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    if _is_heartbeat_message(row):
        return "BigData HeartBeat Message"
    else:
        return row["summary"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _top_level(row: pd.Series) -> str:
    """Indicate the alarm top level."""
    return row["a_code"].upper()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_type(row: pd.Series) -> str:
    if _is_heartbeat_message(row):
        return enums.AlarmType.HEARTBEAT.value

    match row["status"]:
        case "resolved":
            return enums.AlarmType.RESOLUTION.value
        case _:
            return enums.AlarmType.PROBLEM.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> datetime.datetime | None:
    if _event_type(row) == enums.AlarmType.RESOLUTION.value:
        return _use_provided_date_when_possible(row["ends_at"])
    else:
        return None


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _handle_time() -> datetime.datetime:
    """Determine the handle time, i.e. the time when the alarm is handled by Olympus."""
    return utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime.datetime:
    return row["raise_time"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _use_provided_date_when_possible(provided_date: str | None) -> datetime.datetime:
    """Fetch the date preferably from the provided date, else set as now."""
    if provided_date:
        try:
            return utils.parse_datetime(date_str=provided_date)
        except ParserError:
            """Failed to parse to a datetime, we will return a now() later. We're not required to read
            the provided date anyway."""

    return utils.now_naive()


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df_records: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    df_records.fillna("N/A", inplace=True)

    # Hardcoded fields
    df_records["agent_id"] = agent_id
    df_records["clear_type"] = _clear_type()
    df_records["manager"] = _manager()
    df_records["action_class"] = _scope()
    df_records["handle_time"] = _handle_time()

    # Computed fields
    df_records["raise_time"] = df_records.apply(_raise_time, axis=1)
    df_records["additional_data"] = df_records.apply(_additional_data, axis=1)
    df_records["top_level"] = df_records.apply(_top_level, axis=1)
    df_records["actionable"] = df_records.apply(_actionable, axis=1)
    df_records["ci_id"] = df_records.apply(_ci_id, axis=1)
    df_records["node"] = df_records.apply(_node, axis=1)
    df_records["clear_time"] = df_records.apply(_clear_time, axis=1)
    df_records["metric_type"] = df_records.apply(_metric_type, axis=1)
    df_records["metric_name"] = df_records.apply(_metric_name, axis=1)
    df_records["node_alias"] = df_records.apply(_node_alias, axis=1)
    df_records["summary"] = df_records.apply(_summary, axis=1)
    df_records["severity"] = df_records.apply(_severity, axis=1)
    df_records["event_type"] = df_records.apply(_event_type, axis=1)
    df_records["wake_up_time"] = df_records.apply(_wake_up_time, axis=1)

    return df_records
