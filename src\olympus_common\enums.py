"""Module to provide the enums used in the olympus project.

Notes
-----
- To ensure that psycopg inserts enums properly, it is advised to always use `str, Enum` or `IntEnum`.
- Regular enums behave in a way that when comparing a value to a member, it returns False.
  However, when using `str, Enum` or `IntEnum`, due to mro, the `__eq__` of str or int is found respectively. Due to
  this, comparing an enum's value to the member would return True.
  Following code-snippet shows this:

    >>> from enum import Enum, IntEnum
    >>> class MyEnum(Enum):
    ...     OPT = 1
    ...
    >>> MyEnum.OPT.value == MyEnum.OPT
    False
    >>> class MyStrEnum(str, Enum):
    ...     OPT = "OPT"
    ...
    >>> MyStrEnum.OPT.value == MyStrEnum.OPT
    True
    >>> from enum import IntEnum
    >>> class MyIntEnum(IntEnum):
    ...     OPT = 0
    ...
    >>> MyIntEnum.OPT.value == MyIntEnum.OPT
    True

"""

from enum import Enum, IntEnum


class AlarmType(Enum):
    """Represent the possible values for an AlarmType."""

    PROBLEM = "problem"
    RESOLUTION = "clear"
    HEARTBEAT = "heartbeat"


class Severity(IntEnum):
    """Represent the possible values for an Alarm's severity."""

    CLEARED = 0
    INDETERMINATE = 1
    WARNING = 2
    MINOR = 3
    MAJOR = 4
    CRITICAL = 5


class ClearType(str, Enum):
    """Represent the possible values for an Alarm's Clear."""

    NOT_DEFINED = "not_defined"
    MANUALLY = "manual"
    AUTOMATICALLY = "automatic"


class Scope(str, Enum):
    """Represent the possible values for an Alarm's Scope_ref."""

    IT = "IT"
    TE = "TE"
    NETWORKS = "NETWORKS"


class IcingaObjectType(str, Enum):
    """An enum to validate the Icinga Object Type."""

    SERVICE = "Service"
    HOST = "Host"
    DEPENDENCY = "Dependency"
    HOST_GROUP = "HostGroup"
    SERVICE_GROUP = "ServiceGroup"


class IcingaServiceStatus(IntEnum):
    """An enum representing the Icinga Service Exit status."""

    OK = 0
    UNKNOWN = 3
    WARNING = 1
    CRITICAL = 2


class IcingaHostStatus(IntEnum):
    """An enum representing the Icinga Host Exit Status."""

    OK = 0
    CRITICAL = 1


class AlarmJobStatus(IntEnum):
    """Represent the possible values for a status on an alarm job."""

    TO_DO = 0
    DONE = 1
    IN_ERROR = 2
    NOT_DONE = 3


class SapMainAlarm(str, Enum):
    """Represent the values to know the main alarm for a SAP incident."""

    YES = "Yes"
    NO = "No"
    UNKNOWN = "N/A"


class AirStatus(str, Enum):
    """Represent the possible values for an Air Status."""

    CREATED = "Created"
    PENDING = "Pending"
    WORKING = "Working"
    ENDED = "Ended"
    CANCELLED = "Cancelled"


class HeartbeatType(str, Enum):
    """Represent the possible values for an Heartbeat Type."""

    IDLE_TIME = "idle_time"
    PERIOD = "period"


class ReleaseStatus(str, Enum):
    """Represent the possible values for a Release Status."""

    COMPLETED = "E0004"
    CANCELLED = "E0001"
    IN_PREPARATION = "E0003"
    APPROVED = "E0022"
    NEXT_DRM = "E0024"
    IN_EXECUTION = "E0019"


class IncidentStatus(str, Enum):
    """Represent the possible values for an Incident Status."""

    RESOLVED = "RESO"
    CANCELED = "CANC"
    COMPLETED = "COMP"
    ASSIGNED = "ASSI"
    PENDING = "PEND"


class MeasureType(str, Enum):
    """Represent possible transaction type for Elastic APM.

    Notes
    -----
    db_query: Function that use a db query
    request: Function executed via an HTTP or SQL request
    task: Function called in an asynchronous job
    messaging: Function triggered after an MQ message
    scheduled: Function executed in a cron/batch
    kafka: Function use to kafka processes
    custom: Function without specific context
    """

    DB_QUERY = "db_query"
    REQUEST = "request"
    TASK = "task"
    MESSAGING = "messaging"
    SCHEDULED = "scheduled"
    KAFKA = "kafka"
    CUSTOM = "custom"


class ZabbixCC(str, Enum):
    """Represent the possible competence center that mon-zabbix can have."""

    LCC = "lcc"
    UCC = "ucc"
    ICTOPS = "ictops"
    SCADA = "sca"
