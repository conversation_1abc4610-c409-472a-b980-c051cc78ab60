"""Details Design module for mon-ca-spectrum."""

import json
from datetime import datetime, timedelta, timezone

import pandas as pd
from sqlalchemy.orm import Session

from mon_ca_spectrum import patterns
from olympus_common import db, enums, utils
from olympus_common.ca_spectrum import CA_SPECTRUM_METRICS
from olympus_common.elastic_apm import CaptureSpan, trace_scan

NOT_AVAILABLE = "N/A"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _actionable_alarm(row: pd.Series) -> bool:
    """Define the actionable alarm property.

    Datalines (antce-* and Dt.R/St.R):
    This logic is used to always make hirshmann alarms and datalines alarms actionnable.
    Enrichment will also be skipped for these assettypes in enrichment-client.

    The rest of the original Optic logic is commented out.

    hsphili51, hsgespi17:
    These were switches used by local support to test PC's
    -> they caused interface up/down errors when a new PC was tested.
    After investigation, the switch in ringstation doesn't even exist anymore in UCMDB.
    -> if the exception is still needed, it will be implemented by desactivating the metric in the UCMDB.

    Other rules:
    any other path returned False anyway.

    """
    ci_id_lower: str = row["ci_id"].lower()
    return ci_id_lower.startswith("antce-") or ci_id_lower.startswith("dt.r") or ci_id_lower.startswith("st.r")


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _additional_data(row: pd.Series) -> str:
    """Define the additional infos for the alarm."""
    if row["cause_code"] == "fff002e9":
        return json.dumps({"cause_code": row["cause_code"], "f5_pool": row["f5_pool"]})
    elif "fff0031" in row["cause_code"]:
        return json.dumps({"cause_code": row["cause_code"], "syslog_event": row["syslog_event"]})
    else:
        return json.dumps({"cause_code": row["cause_code"]})


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _f5_pool(row: pd.Series) -> str:
    """Extract the pool name from the event message field if it's possible.

    The value is an empty string if the regex doesn't match.
    """
    if regex_result := patterns.pool_name_regex1.search(row["event_message"]):
        pool_name = regex_result[1]
        splitted_pool_name = pool_name.split("/")
        return f"{splitted_pool_name[1]}/{splitted_pool_name[-1]}"
    elif regex_result := patterns.pool_name_regex2.search(row["event_message"]):
        return regex_result[1]
    elif regex_result := patterns.pool_name_regex3.search(row["event_message"]):
        return regex_result[1]
    else:
        return NOT_AVAILABLE


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _syslog_event(row: pd.Series) -> str:
    """Extract the syslog event from the event message field if it's possible.

    The value is an empty string if the regex doesn't match.
    """
    if regex_result := patterns.syslog_event_regex.match(row["event_message"]):
        return regex_result[2].replace(" ", "")
    else:
        return NOT_AVAILABLE


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Define the ci_id property of the alarm."""
    # Quick data analysis on the "mname" field, as of 2024-06-04:
    # - 535k events since 2024-03-04
    # - 390k events with mname like *.railb.be*
    #   -> top 5 values: f5mechl1-prod.msnet.railb.be, wlc.msnet.railb.be, f5mechl2-prod.msnet.railb.be,
    #                    rmmarlo1v-m.network.railb.be, rmblock1v-m.network.railb.be
    # - 7k events with mname like *.b-rail.be*
    #   -> top 5 values: fwpeile2.b-rail.be, ASA fwbrumz1.b-rail.be, ASA fwbrumz1-fail.b-rail.be,
    #                    ASA fwbrumz1-fail.b-rail.be, ASA fwbrumz2-fail.b-rail.be (preprod)
    # - 885 events with mname not like *.railb.be* and not like *.b-rail.be* and like "*_*"
    #   -> top 5 values: POC_Sentinel, APr_A1984, acmechl1_eth1/2, APr_A1984_eth1/2, acmechl1_eth2/2
    # - 137k events with mname not like *.railb.be* and not like *.b-rail.be* and not like "*_*"
    #   -> top 5 values: Fault Isolation, APIC Cluster, RsyslogServer, lpbrain12, ACI Muizen
    # - 5k events with cause_code = fff002e9
    #   -> Before, there was a logic to delete the ".msnet.railb.be" suffix after having split "railb.be".
    #      Obviously this made no sense.
    # - 208k events with mname like rm*
    #   -> top 5 values: rmblock1v-m.network.railb.be, rmblock1v-m.network.railb.be_et-10/1/2,
    #                    rmcharl2v-m.network.railb.be, rmmarlo1v-m.network.railb.be, rmnamur1v-m.network.railb.be
    # - 12k events with mname like am* (nothing like AM*, Am* or aM*). Same for ar* (but 63 events)
    #   -> top 5 values: amliege2v-m.network.railb.be, ambrugg2v-m.network.railb.be, amgezee1v-m.network.railb.be,
    #                    amhasse1v-m.network.railb.be, ammonsx1v-m.network.railb.be
    model_name: str = row["mname"]
    f5_pool: str = row["f5_pool"]
    # First step A: remove everything after "." if like ".railb.be" or "".b-rail.be"
    if ".b-rail.be" in model_name or ".railb.be" in model_name:
        ci_id = model_name.split(".", maxsplit=1)[0]
    else:
        # First step B: remove everything after "_"
        ci_id = model_name.split("_", maxsplit=1)[0]

    # Second step: if empty, take ipaddress.
    if ci_id.isspace():
        ci_id = row["ipaddress"]

    # Third step: special case of metric fff002e9.
    if row["cause_code"] == "fff002e9":
        model_name_ = model_name.removesuffix(".msnet.railb.be")
        ci_id = f"{f5_pool}:{model_name_}"

    # Fourth step: special case of metrics fff0032f, fff003a6 or fff003a7.
    if row["cause_code"] in ["fff0032f", "fff003a6", "fff003a7"]:
        ci_id = f5_pool

    # Fifth step: RsyslogServer.
    if model_name.upper() == "RSYSLOGSERVER":
        if row["cause_code"] in ["fff0031c", "fff0031d"]:
            ci_id = "ISESYSLOG"
        elif row["cause_code"] in ["fff00329", "fff0032a", "fff0032b", "fff0032c", "fff0032d"]:
            ci_id = "DNASYSLOG"

    # Sixth step: remove "v", "v-m", "i", "o" at the end.
    if patterns.ci_id_regex1.search(ci_id):
        ci_id = ci_id.removesuffix("v-m")
    elif patterns.ci_id_regex2.search(ci_id):
        ci_id = ci_id[:-1]

    if patterns.ci_id_regex3.search(ci_id):
        ci_id = ci_id.split(".")[3]

    return ci_id


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_type() -> str:
    """Define the clear_type of the alarm (how the alarm is clear)."""
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the name of the manager."""
    return "mon-ca-spectrum"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_type() -> str:
    """Return the metric type.

    Notes
    -----
    For CA Spectrum this value is hardcoded.
    """
    return "/HardwareEvent/"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _metric_name(row: pd.Series) -> str:
    """Define the metric name of the alarm."""
    cause_code = row["cause_code"].rjust(8, "0")
    if cause_code in ["fff0031c", "fff0031d"]:
        return f"{cause_code}_{row['syslog_event']}"
    else:
        return cause_code


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _node_node_alias(row: pd.Series) -> str:
    """Define the node (or the node_alias) for the alarm."""
    return row["ipaddress"]


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_timestamp(row: pd.Series) -> datetime:
    """Return the timestamp of the event.

    This is used for the raise_time property and taken directly from the event_message.
    """
    match = patterns.raisetime_regex.search(row["event_message"])

    if match:
        day, month_abbr, year, time_str = match.groups()
        month_number = datetime.strptime(month_abbr, "%b").month
        _timestamp = datetime.strptime(f"{month_number:02d}/{day}/{year} {time_str}", r"%m/%d/%Y %H:%M:%S")
        _timestamp_tz = _timestamp.astimezone(timezone.utc).replace(tzinfo=None)
        return _timestamp_tz
    else:
        return utils.now_naive()


def _delay() -> int:
    """Return the delay."""
    return 120


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime:
    """Return the wake up time of the event."""
    return row["raise_time"] + timedelta(0, row["delay"])


def _scope() -> str:
    """Return the scope."""
    return enums.Scope.IT.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Define the severity of the alarm."""
    # Severity depends on the CI_ID being PROD or non-PROD
    ci_id: str = row["ci_id"]
    ci_id_upper: str = ci_id.upper()
    key = "severity_prod"
    if patterns.severity_regex.match(ci_id_upper):
        key = "severity_nonprod"
    return int(CA_SPECTRUM_METRICS.get(row["metric_name"], {}).get(key, enums.Severity.INDETERMINATE.value))


def _handle_time() -> datetime:
    """Return the handle time of the event."""
    return utils.now_naive()


def _alert_group(row: pd.Series) -> str:
    """Define the alert_group property for the alarm."""
    get_alert_group_msg: str = str(CA_SPECTRUM_METRICS.get(row["metric_name"], {}).get("summary", ""))
    f5_pool: str = row["f5_pool"]
    if row["cause_code"] == "fff002e9":
        f5_pool_splitted = f5_pool.split("/")
        alert_group = f"{f5_pool_splitted[0]}{f5_pool_splitted[-1]}"
    else:
        alert_group = get_alert_group_msg
    return alert_group


def _alert_key(row: pd.Series) -> str:
    """Define the alert_key property for the alarm."""
    return f"{row['mtype']} - {row['mname']}"


def _identifier(row: pd.Series) -> str:
    """Define the identifier of the alarm."""
    return f"{row['node_alias']} {_alert_group(row)} {_alert_key(row)}"


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Define the summary of the alarm."""
    summary = str(CA_SPECTRUM_METRICS.get(row["metric_name"], {}).get("alert_group", ""))

    if row["cause_code"] == "fff002e9":
        summary = f"{summary}_{row['f5_pool']}"
    elif "_" in row["mname"]:
        summary = f"{summary}. Interface Number: {row['mname'].rsplit('_')[-1]}".strip(" .")

    # if metric not in list of known metrics, return original event messsage
    if len(summary) == 0:
        if regex_match := patterns.summary_regex.match(row["event_message"]):
            summary = regex_match[1]
        else:
            summary = row["event_message"]

    return summary


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _event_type(row: pd.Series) -> str:
    """Define if the alarm is a clear or a problem."""
    if (
        int(row["raw_event_type"]) == 0
        or row["raw_severity"] in ["CLEARED", "NORMAL"]
        or (row["cause_code"] == "fff002e9" and int(row["raw_event_type"]) != 1 and row["f5_pool"] == NOT_AVAILABLE)
    ):
        return enums.AlarmType.RESOLUTION.value
    else:
        return enums.AlarmType.PROBLEM.value


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _clear_timestamp(row: pd.Series) -> datetime | None:
    """Define the clear_timestamp of the alarm."""
    if row["event_type"] == enums.AlarmType.PROBLEM.value:
        return None
    else:
        return utils.now_naive()


@trace_scan(transaction_type=enums.MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the dd script."""
    agent_id = db.Agent.get_agent_id_from_name("CA_Spectrum", session)
    return _transform(df, agent_id)


@CaptureSpan(span_type=enums.MeasureType.CUSTOM.value)
def _transform(df: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    """Return the dataframe with all the details design performed."""
    # Temporary value required for the DD
    df["f5_pool"] = df.apply(_f5_pool, axis=1)
    df["syslog_event"] = df.apply(_syslog_event, axis=1)

    # Hardcoded values
    df["metric_type"] = _metric_type()
    df["agent_id"] = agent_id
    df["manager"] = _manager()
    df["action_class"] = _scope()
    df["clear_type"] = _clear_type()
    df["delay"] = _delay()
    df["handle_time"] = _handle_time()
    # Applied values
    df["event_type"] = df.apply(_event_type, axis=1)
    df["ci_id"] = df.apply(_ci_id, axis=1)
    df["node"] = df.apply(_node_node_alias, axis=1)
    df["node_alias"] = df["node"]
    df["metric_name"] = df.apply(_metric_name, axis=1)
    df["summary"] = df.apply(_summary, axis=1)  # dependant on metric_name
    df["clear_time"] = df.apply(_clear_timestamp, axis=1)
    df["event_id"] = df["raw_event_id"]
    df["actionable"] = df.apply(_actionable_alarm, axis=1)
    df["severity"] = df.apply(_severity, axis=1)  # dependant on ci_id and metric_name
    df["identifier"] = df.apply(_identifier, axis=1)
    df["additional_data"] = df.apply(_additional_data, axis=1)
    df["raise_time"] = df.apply(_event_timestamp, axis=1)

    # Snooze fields check for wake_up_time
    df["wake_up_time"] = df.apply(_wake_up_time, axis=1)

    return df
