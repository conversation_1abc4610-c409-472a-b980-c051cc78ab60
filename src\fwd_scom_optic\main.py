"""Entrypoint for the application."""

from fwd_scom_optic import datawriters, dd
from fwd_scom_optic.config import config
from olympus_common import core, datareaders, db, defaults
from olympus_common.elastic_apm import trace_scan
from olympus_common.enums import MeasureType
from olympus_common.monitoring import MonScriptManager


@trace_scan(MeasureType.CUSTOM.value)
def syslogwriter_kafkareader_app() -> core.Application:
    """Return an Application with a SyslogWriter and KafkaReader."""
    session = db.create_session(config.service_name)
    datareader = datareaders.KafkaReader()
    logger = defaults.get_logger(config.debug, config.logger_config)
    datawriter = datawriters.SyslogWriter()
    monitoring_manager = MonScriptManager(application_name=config.service_name, db_session=session)
    return core.Application(
        datareader=datareader,
        datawriter=datawriter,
        logger=logger,
        monitoring_manager=monitoring_manager,
        backend_db_session=session,
    )


application = syslogwriter_kafkareader_app()


@application.run_forever(sleep_time=config.sleep_time)
@trace_scan(MeasureType.CUSTOM.value)
def main(data: list[dict]) -> list[dict]:
    """Run the dd.

    This function is the starting point of the dd and should be used to run the dd.
    """
    return defaults.transform(data, config, dd.run, application.backend_db_session, sort_keys=None, deduplicate=False)
