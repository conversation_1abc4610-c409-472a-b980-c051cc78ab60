"""Entrypoint for the application."""

from mon_websphere_mq import dd
from mon_websphere_mq.config import config
from mon_websphere_mq.oid_mapper import OIDMapper
from olympus_common import defaults

application = defaults.databasewriter_kafkareader_app(config)


@application.run_forever(sleep_time=config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Run the dd.

    This function is the starting point of the dd and should be to run the dd.
    """
    return defaults.transform(data, config, dd.run, application.backend_db_session, oid_mapper=OIDMapper)
