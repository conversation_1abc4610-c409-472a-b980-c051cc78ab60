"""Module to interact with AI-Reactivity."""

import datetime as dt
import logging
from dataclasses import dataclass

import requests
from requests import Response, Session

from olympus_common import enums, icinga
from olympus_common.datareaders import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from olympus_common.db import Incident, create_session
from olympus_common.elastic_apm import CaptureSpan
from olympus_common.enums import MeasureType
from sap_air_client.config import config
from sap_air_client.models import AirEntity
from sap_air_client.utils import construct_service_name, handle_air_request


@dataclass
class Air:
    """Represents the object to interact with te AI-Reactivity endpoint."""

    user_credentials: dict
    air_url: str
    token_expiration: dt.datetime = dt.datetime.now()
    class_name: str = "AIReactivity"

    session: Session = Session()
    session.verify = False
    client = icinga.IcingaClient(use_logging=True)

    @property
    def get_token(self) -> str:
        """Get the AIR token."""
        resp = requests.post(url=self.user_credentials["token_url"], data=self.user_credentials, timeout=60)
        resp.raise_for_status()  # Potentially raises requests.HTTPError
        data = resp.json()
        return data["access_token"]

    def _login(self) -> None:
        """Add the authorization in header if it is not present."""
        if self.token_expiration <= dt.datetime.now():
            air_token = self.get_token
            auth = {"Authorization": f"Bearer {air_token}"}
            self.session.headers.update(auth)
            self.token_expiration = dt.datetime.now() + dt.timedelta(minutes=60)

    @CaptureSpan(MeasureType.REQUEST.value)
    def _request(self, method, *args, **kwargs) -> Response:
        """Send a request to the AIR endpoint, login if needed."""
        self._login()
        return self.session.request(method, *args, **kwargs)

    @CaptureSpan(MeasureType.REQUEST.value)
    @handle_air_request
    def run_procedure(self, entity: AirEntity) -> Response:
        """Run the AIR procedure."""
        data = {
            "Ci": entity.identification,
            "FlocID": entity.floc_id,
            "Metric": entity.metric_type,
            "MonitoredElementName": entity.metric_name,
            "Model": entity.model,
            "MetricSubGroup": entity.subgroups,
            "ACodes": entity.a_codes,
            "SAPIncidentID": entity.sap_incident_id,
            "Severity": entity.severity,
        }
        return self._request("post", url=self.air_url, json=data)

    @CaptureSpan(MeasureType.MESSAGING.value)
    def read_kafka_topic(self, kafka_reader: KafkaReader) -> None:
        """Read the AI-reactivity kafka topic."""
        r_data = kafka_reader.read_data()
        try:
            for data in r_data:
                if data["Status"] == "Created":
                    # Do nothing for Created events.
                    continue

                sap_id = data.pop("SapNotificationID")
                air_base_url = f"{config.air.url_con.split('/')[0]}//{config.air.url_con.split('/')[2]}"
                air_base_url = air_base_url.replace("-api", "")
                data["Link"] = f"{air_base_url}/events/{data['EventID']}?executionID={data['ExecutionID']}"
                # 1 Update metrics in Icinga
                if metrics := self.get_metric_from_sap_id_icinga(sap_id):
                    for metric in metrics:
                        self.update_in_icinga(
                            service_name=metric,
                            floc_id=None,
                            metric_type=None,
                            metric_name=None,
                            data=data,
                        )
                    # 2 Persist execution status in DB
                    session = create_session(config.service_name)
                    try:
                        if incident := Incident.get_by_sap_id(sap_id=sap_id, session=session):
                            Incident.update_air_data(
                                id=incident.id,
                                execution_id=data["ExecutionID"],
                                instruction_id=data["EventID"],
                                air_status=data["Status"],
                                session=session,
                            )
                        session.commit()
                        session.close()
                    except Exception as exc:
                        session.close()
                        raise Exception(exc) from exc
                else:
                    logging.debug(f"Incident ID {sap_id} found in frontend.")
        except Exception as exc:
            logging.debug(f"Exception while reading kafka: {exc}")
            raise exc
        finally:
            """Release resources at the end of the execution."""
            kafka_reader.success(r_data)

    @CaptureSpan(MeasureType.REQUEST.value)
    def update_in_icinga(
        self,
        service_name: str | None,
        floc_id: str | None,
        metric_type: str | None,
        metric_name: str | None,
        data: dict[str, str],
        objet_type: str = enums.IcingaObjectType.SERVICE.value,
    ) -> list[dict]:
        """Update the procedure ID, execution ID and procedure status on icinga."""
        if not service_name:
            service_name = icinga.encode_string(construct_service_name(floc_id, metric_type, metric_name))

        return self.client.update(
            object_type=objet_type,
            name=service_name,
            attrs={"attrs": icinga.format_icinga_attrs(module_name=self.class_name, attrs=data)},
        )

    @CaptureSpan(MeasureType.REQUEST.value)
    def get_metric_from_sap_id_icinga(
        self,
        sap_id: str,
        match_field: str = "service.vars.Sap.Qmnum",
        object_type=enums.IcingaObjectType.SERVICE.value,
    ) -> list:
        """Get metrics information according the SAP ticket ID."""
        metrics = self.client.get_all(
            object_type=object_type, filters=f'match("{sap_id}",  {match_field})', attrs=["vars"]
        )
        if len(metrics) != 0:
            metrics = [icinga.encode_string(metric["name"]) for metric in metrics]
        return metrics


air = Air(user_credentials=config.air.api_credentials, air_url=config.air.url_con)
