from typing import Protocol


class KafkaError(Protocol):
    """Represent a protocol that types an object as a KafkaError."""

    def code(self) -> int:
        """Return the error code."""
        ...

    def str(self) -> str:
        """Return the error string."""
        ...

    def is_fatal(self) -> bool:
        """Return whether the error is fatal."""
        ...


class KafkaMessage(Protocol):
    """Represent a protocol that types an object as a KafkaMessage."""

    def topic(self) -> str:
        """Return the topic of the message."""
        ...

    def partition(self) -> int:
        """Return the partition of the message."""
        ...

    def offset(self) -> int:
        """Return the offset of the message."""
        ...

    def key(self) -> bytes | None:
        """Return the key of the message."""
        ...

    def value(self) -> bytes | None:
        """Return the value of the message."""
        ...

    def error(self) -> KafkaError | None:
        """Return the error associated with the message, if any."""
        ...


class KafkaConsumer(Protocol):
    """Represent a protocol that types an object as a KafkaConsumer."""

    def poll(self, timeout: float) -> KafkaMessage:
        """Poll for messages from the Kafka topic."""
        ...

    def commit(self) -> None:
        """Commit the offsets of the messages consumed."""
        ...

    def close(self) -> None:
        """Close the consumer."""
        ...

    def subscribe(self, topics: list[str]) -> None:
        """Subscribe to the given topics."""
        ...

    def assign(self, partitions: list[KafkaMessage]) -> None:
        """Assign specific partitions to the consumer."""
        ...
