"""Detail design implementation for mon-struxureware."""

import datetime
import json
import re

import pandas as pd
from sqlalchemy.orm import Session

from olympus_common import db, enums, utils
from olympus_common.elastic_apm import CaptureSpan, trace_scan
from olympus_common.enums import MeasureType


@CaptureSpan(MeasureType.CUSTOM.value)
def _is_heartbeat(row: pd.Series) -> bool:
    """Determine if the message is a heartbeat."""
    return row["event_name"] == "Heartbeat"


@CaptureSpan(MeasureType.CUSTOM.value)
def _alert_group(row: pd.Series) -> str:
    """Create the alert group for the alarm.

    Parameters
    ----------
    row : pd.Series
        The row relative to the event.

    Returns
    -------
    str
        The alert group of the alert; this will be the event name if it's present else the description.
    """
    return row["event_name"] or row["description"]


@CaptureSpan(MeasureType.CUSTOM.value)
def _monitored_element_name(row: pd.Series) -> str:
    """Create the monitored element name for the event.

    Parameters
    ----------
    row : pd.Series
        The row relative to the event.

    Returns
    -------
    str
        The monitored element name of the event.
    """
    if _is_heartbeat(row):
        return "Heartbeat"

    upper_alert_group = _alert_group(row).upper()
    if re.match(r"^EXTERNAL - D[12]", upper_alert_group):
        return upper_alert_group
    elif re.match(r"^INVERTER - MAJOR", upper_alert_group):
        return "INVERTER - MAJOR"
    elif re.match(r"^INVERTER", upper_alert_group):
        return "INVERTER - MINOR"
    elif re.match(r"^RECTIFIER - MAJOR", upper_alert_group):
        return "RECTIFIER - MAJOR"
    elif re.match(r"^RECTIFIER", upper_alert_group):
        return "RECTIFIER - MINOR"
    elif re.match(r"^SYSTEM - MAJOR", upper_alert_group):
        return "SYSTEM - MAJOR"
    elif re.match(r"^SYSTEM", upper_alert_group):
        return "SYSTEM - MINOR"
    elif re.match(r"ZONE.* - .", upper_alert_group):
        if match := re.match(r"^(ZONE.* - .)", upper_alert_group):
            return match[0]
        else:
            return "N/A"
    elif match := re.match(r"^([^ ]+)", upper_alert_group):
        return match[0]
    else:
        return "N/A"


@CaptureSpan(MeasureType.CUSTOM.value)
def _ci_id(row: pd.Series) -> str:
    """Create the CI ID for the event.

    Parameters
    ----------
    row : pd.Series
        The row relative to the event.

    Returns
    -------
    str
        The CI ID for the event.
    """
    if _is_heartbeat(row):
        return "Struxurware_hb"

    hostname: str = row["hostname"]
    if hostname.startswith("("):
        return hostname.split("(", maxsplit=1)[1].split(")", maxsplit=1)[0].upper().strip()
    elif "(" in hostname:
        return hostname.split("(")[0].upper().strip()
    else:
        return hostname.upper().strip()


@CaptureSpan(MeasureType.CUSTOM.value)
def _severity(row: pd.Series) -> int:
    """Create a severity (int code of it) for the event."""
    if _is_heartbeat(row):
        return enums.Severity.INDETERMINATE.value

    resolve_time_: str = row["resolve_time"]
    severity_: int = row["severity"]
    if int(resolve_time_) != 0:
        # This is a clear event
        return enums.Severity.CLEARED.value
    if int(severity_) == 1:
        return enums.Severity.WARNING.value
    elif int(severity_) == 2:
        return enums.Severity.MINOR.value
    elif int(severity_) == 3:
        return enums.Severity.MAJOR.value
    else:
        return enums.Severity.CRITICAL.value


@CaptureSpan(MeasureType.CUSTOM.value)
def _event_type(row: pd.Series) -> str:
    """Create the clear level for the event.

    Parameters
    ----------
    row : pd.Series
        The row relative to the event.

    Returns
    -------
    str
        The clear level for the event.
    """
    if _is_heartbeat(row):
        return enums.AlarmType.HEARTBEAT.value
    elif str(row["resolve_time"]) == "0":
        return enums.AlarmType.PROBLEM.value
    else:
        return enums.AlarmType.RESOLUTION.value


@CaptureSpan(MeasureType.CUSTOM.value)
def _raise_time(row: pd.Series) -> datetime.datetime:
    """Create the raise time for the event."""
    return (
        datetime.datetime.fromtimestamp(int(row["notify_time"]), tz=datetime.timezone.utc).replace(tzinfo=None)
        if _event_type(row) in [enums.AlarmType.PROBLEM.value, enums.AlarmType.HEARTBEAT.value]
        else datetime.datetime.fromtimestamp(int(row["resolve_time"]), tz=datetime.timezone.utc).replace(tzinfo=None)
    )


@CaptureSpan(MeasureType.CUSTOM.value)
def _clear_time(row: pd.Series) -> datetime.datetime | None:
    """Create the clear time for the event."""
    return row["raise_time"] if _event_type(row) == enums.AlarmType.RESOLUTION.value else None


@CaptureSpan(MeasureType.CUSTOM.value)
def _handle_time() -> datetime.datetime:
    """Create the handle time for the event."""
    return utils.now_naive()


@CaptureSpan(MeasureType.CUSTOM.value)
def _extended_attr(row: pd.Series) -> str | None:
    """Return the extended attribute based on the given parameters.

    Parameters
    ----------
    row : pd.Series
        The row relative to the event.

    Returns
    -------
    str | None
        Extended attributes for the event. Here, mainly contains Actionable state and the delay. If the delay is 0, None
        is returned.
    """
    return json.dumps({"delay": _delay(row), "event_name": row["event_name"]})


@CaptureSpan(MeasureType.CUSTOM.value)
def _delay(row: pd.Series) -> int:
    """Return the delay for the event."""
    summary_: str = _summary(row)
    upper_alert_group: str = _alert_group(row).upper()
    severity_: int = _severity(row)
    if re.match(r"ZONE. - F.\...:", summary_):
        return 600
    elif "COMMUNICATION LOST" in upper_alert_group:
        return 900
    elif ("VOLTAGE - BATTERY VOLTAGE" in upper_alert_group) or ("BATTERY - TIME BEFORE SHUTDOWN" in upper_alert_group):
        return 30
    elif severity_ < 5:
        return 130
    else:
        return 0


@CaptureSpan(MeasureType.CUSTOM.value)
def _wake_up_time(row: pd.Series) -> datetime.datetime:
    """Return the wake-up time based on the delay."""
    delay = _delay(row)
    return _raise_time(row) + datetime.timedelta(0, delay)


@CaptureSpan(MeasureType.CUSTOM.value)
def _node(row: pd.Series) -> str:
    """Create the node for the event.

    Parameters
    ----------
    row : pd.Series
        The row relative to the event.

    Returns
    -------
    str
        The node for this event. Normally an IP address.
    """
    hostname_: str = row["hostname"]
    if "(" in hostname_ and ")" in hostname_:
        return hostname_.split("(", maxsplit=1)[1].split(")", maxsplit=1)[0]
    else:
        return _ci_id(row)


@CaptureSpan(MeasureType.CUSTOM.value)
def _node_alias(row: pd.Series) -> str:
    """Create the node alias for the event.

    Parameters
    ----------
    row : pd.Series
        The row relative to the event.

    Returns
    -------
    str
        The node alias of the event (which is the ci_id).
    """
    return _ci_id(row)


@CaptureSpan(MeasureType.CUSTOM.value)
def _summary(row: pd.Series) -> str:
    """Create the summary for the event.

    Parameters
    ----------
    row : pd.Series
        The row relative to the event.

    Returns
    -------
    str
        The summary of the event.
    """
    if _is_heartbeat(row):
        return "Heartbeat"

    sensor_value_1: str = row["sensor_value_1"]
    description: str = row["description"]
    if event_name := row["event_name"]:
        if sensor_value_1 and sensor_value_1 != "N/A":
            return f"{event_name}: {sensor_value_1}."
        else:
            return f"{event_name}: {description}."
    elif sensor_value_1 and sensor_value_1 != "N/A":
        return f"{sensor_value_1}."
    else:
        return f"{description}."


@CaptureSpan(MeasureType.CUSTOM.value)
def _metrics(row: pd.Series) -> str:
    """Return the metrics for this event."""
    if _is_heartbeat(row):
        return "/ApplicationEvent/"

    return "/HardwareEvent/"


@CaptureSpan(MeasureType.CUSTOM.value)
def _scope() -> str:
    """Return the scope for this event."""
    return enums.Scope.TE.value


@CaptureSpan(MeasureType.CUSTOM.value)
def _manager() -> str:
    """Return the manager for this event."""
    return "mon-struxureware"


@CaptureSpan(MeasureType.CUSTOM.value)
def _top_level() -> str:
    """Return the top_level for this event."""
    return "A1421"


@CaptureSpan(MeasureType.CUSTOM.value)
def _clear_type() -> str:
    """Return the type of clear for the DD.

    Here clear type is automatically (received by EMS).
    """
    return enums.ClearType.AUTOMATICALLY.value


@CaptureSpan(MeasureType.CUSTOM.value)
def _identifier(row: pd.Series) -> str:
    """Return the identifier for this event.

    This is different from OPTIC in the sense that we do not add an event_type
    This is because OPTIC handles clear and problems in the same manner, but Olympus does not.
    """
    return f"{row['agent_id']}/{row['node']}/{_alert_group(row)}/{row['metric_name']}"


@trace_scan(MeasureType.CUSTOM.value)
def run(df: pd.DataFrame, session: Session | None) -> pd.DataFrame:
    """Run the dd-script."""
    agent_id = db.Agent.get_agent_id_from_name("StruxureWare", session)
    return _transform(df, agent_id)


@CaptureSpan(MeasureType.CUSTOM.value)
def _transform(df_records: pd.DataFrame, agent_id: int) -> pd.DataFrame:
    # Set hardcoded data
    df_records["agent_id"] = agent_id
    df_records["clear_type"] = _clear_type()
    df_records["manager"] = _manager()
    df_records["action_class"] = _scope()
    df_records["top_level"] = _top_level()
    df_records["event_id"] = df_records["error_id"]
    df_records["handle_time"] = _handle_time()

    # Set data from the dd's instructions
    df_records["metric_type"] = df_records.apply(_metrics, axis=1)
    df_records["metric_name"] = df_records.apply(_monitored_element_name, axis=1)
    df_records["ci_id"] = df_records.apply(_ci_id, axis=1)
    df_records["severity"] = df_records.apply(_severity, axis=1)
    df_records["event_type"] = df_records.apply(_event_type, axis=1)
    df_records["raise_time"] = df_records.apply(_raise_time, axis=1)
    df_records["clear_time"] = df_records.apply(_clear_time, axis=1)
    df_records["node"] = df_records.apply(_node, axis=1)
    df_records["node_alias"] = df_records.apply(_node_alias, axis=1)
    df_records["summary"] = df_records.apply(_summary, axis=1)
    df_records["additional_data"] = df_records.apply(_extended_attr, axis=1)
    df_records["wake_up_time"] = df_records.apply(_wake_up_time, axis=1)
    df_records["identifier"] = df_records.apply(_identifier, axis=1)

    return df_records
