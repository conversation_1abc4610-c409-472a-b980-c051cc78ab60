"""Static module for mon-scom."""

NA_STR = "NA"

metric_lookup_dict = {
    "AD DOMAIN AVAILABILITY HEALTH DEGRADED": {
        "metric_name": "AD_Domain_Availability_Health_Degraded",
        "metric_type": "/ServiceAvailability/",
    },
    "AD DOMAIN PERFORMANCE HEALTH DEGRADED": {
        "metric_name": "AD_Domain_Performance_Health_Degraded",
        "metric_type": "/ServiceAvailability/",
    },
    "AD SITE AVAILABILITY HEALTH DEGRADED": {
        "metric_name": "AD_Site_Availability_Health_Degraded",
        "metric_type": "/ServiceAvailability/",
    },
    "AD SITE PERFORMANCE HEALTH DEGRADED": {
        "metric_name": "AD_Site_Performance_Health_Degraded",
        "metric_type": "/ServiceAvailability/",
    },
    "APPLICATION POOL IS UNAVAILABLE": {
        "metric_name": "IIS_AppPool_Mon",
        "metric_type": "/Middleware/ApplicationPoolAvailability/",
    },
    "AVAILABLE MEGABYTES OF MEMORY IS TOO LOW": {"metric_name": "Memory", "metric_type": "/System/MemoryAvailable/"},
    "CLUSTER SWITCH": {"metric_name": "ClusterSwitch", "metric_type": "/Cluster/ClusterEvent/"},
    "CUSTOM APPLICATION EVENTLOG FAILED": {"metric_name": "<VAR>", "metric_type": "/Event/"},
    "CUSTOM APPLICATION FILECONTENT": {"metric_name": "<VAR>", "metric_type": "/FileContent/"},
    "CUSTOM APPLICATION FILEWATCHER": {"metric_name": "<VAR>", "metric_type": "/FileWatcher/"},
    "CUSTOM APPLICATION PROCESS FAILED": {"metric_name": "<VAR>", "metric_type": "/ProcessAvailability/"},
    "CUSTOM APPLICATION SERVICE FAILED": {"metric_name": "<VAR>", "metric_type": "/ServiceAvailability/"},
    "CUSTOM APPLICATION SHARE FAILED": {"metric_name": "<VAR>", "metric_type": "/ShareAvailability/"},
    "EXCHANGE HEALTH SET": {"metric_name": "ExchangeHealthSet", "metric_type": "/ServiceAvailability/"},
    "FAILED TO CONNECT TO COMPUTER": {"metric_name": "PING", "metric_type": "/System/NetworkConnectivity/"},
    "FREE SPACE (%) FOR CLUSTER DISK ALERT": {"metric_name": "<VAR>", "metric_type": "/LogicalDisk/LogicalDiskSpace/"},
    "FTP SERVER IS UNAVAILABLE": {"metric_name": "FTPSVC", "metric_type": "/ServiceAvailability/"},
    "FTP SITE IS UNAVAILABLE": {"metric_name": "IIS_FTPsite_Mon", "metric_type": "/Middleware/FTPSiteAvailability/"},
    "HEALTH SERVICE HEARTBEAT FAILURE": {"metric_name": "HealthService", "metric_type": "/ServiceAvailability/"},
    "LOGICAL DISK FREE SPACE IS LOW": {"metric_name": "<VAR>", "metric_type": "/LogicalDisk/LogicalDiskSpace/"},
    "MSSQL ON WINDOWS: DATABASE IS IN OFFLINE/RECOVERY PENDING/SUSPECT/EMERGENCY STATE": {
        "metric_name": "SQL_DB_State",
        "metric_type": "/SQLDatabase/",
    },
    "MSSQL ON WINDOWS: DATABASE LOG FILE IS FULL. BACK UP THE TRANSACTION LOG FOR THE DATABASE TO FREE UP SOME LOG SPACE": {  # noqa: E501
        "metric_name": "SQL_Log_DB_Size",
        "metric_type": "/SQLDatabase/",
    },
    "MSSQL ON WINDOWS: DB ENGINE IS IN UNHEALTHY STATE": {
        "metric_name": "SQL_Instance_State",
        "metric_type": "/SQLDatabase/",
    },
    "MSSQL: AVAILABILITY GROUP IS OFFLINE": {"metric_name": "SQL_AVGroup_State", "metric_type": "/SQLDatabase/"},
    "PRINT SPOOLER:  SERVICE STATUS MONITOR": {"metric_name": "spooler", "metric_type": "/ServiceAvailability/"},
    "SERVER ROLE IS UNAVAILABLE": {"metric_name": "IISADMIN", "metric_type": "/ServiceAvailability/"},
    "SKYPE": {"metric_name": "SKYPE", "metric_type": "/ServiceAvailability/"},
    "THE FTP SERVICE IS UNAVAILABLE": {"metric_name": "FTPSVC", "metric_type": "/ServiceAvailability/"},
    "TOTAL CPU UTILIZATION PERCENTAGE IS TOO HIGH": {
        "metric_name": "Processor",
        "metric_type": "/System/ProcessorLoad/",
    },
    "WEB SERVER IS UNAVAILABLE": {"metric_name": "W3SVC", "metric_type": "/ServiceAvailability/"},
    "WEB SITE IS UNAVAILABLE": {"metric_name": "IIS_Website_Mon", "metric_type": "/Middleware/WebSiteAvailability/"},
    "WINDOWS SERVER SERVICE STOPPED": {"metric_name": "lanmanserver", "metric_type": "/ServiceAvailability/"},
    "Default": {"metric_name": "NA", "metric_type": "NA"},
}

custom_metric_lookup_dict = {
    "EVENTLOG": {"metric_type": "/Event/", "class": "200200"},
    "FILECONTENT": {"metric_type": "/FileContent/", "class": "200200"},
    "FILEWATCHER": {"metric_type": "/FileWatcher/", "class": "200200"},
    "PROCESS": {"metric_type": "/ProcessAvailability/", "class": "200201"},
    "SERVICE": {"metric_type": "/ServiceAvailability/", "class": "200202"},
    "SHARE": {"metric_type": "/ShareAvailability/", "class": "200200"},
    "Default": {"metric_type": "NA", "class": "200200"},
}


NA_VALUES = ["NULL", "", "NUL", "N/A", "NA"]
