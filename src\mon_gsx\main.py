"""Entrypoint for the application."""

from mon_gsx import dd
from mon_gsx.config import config
from mon_gsx.oid_mapper import OIDMapper
from olympus_common import defaults

application = defaults.databasewriter_kafkareader_app(config)


@application.run_forever(config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Execute the main function for when the project is run."""
    return defaults.transform(data, config, dd.run, application.backend_db_session, oid_mapper=OIDMapper)
