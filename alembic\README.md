# Alembic Database Migrations

This directory contains the Alembic configuration and migration files for the Olympus project.

## Overview

Alembic is a database migration tool for SQLAlchemy that allows you to:
- Track database schema changes over time
- Apply incremental changes to your database
- Rollback changes if needed
- Generate migrations automatically from model changes

## Setup

### 1. Environment Configuration

Copy the `.env.example` file to `.env` and configure your database connection:

```bash
cp .env.example .env
```

Edit `.env` with your database credentials:
```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_username
DB_PASSWORD=your_password
DB_SCHEMA=d2110
```

### 2. Initial Setup

If you're setting up Alembic for the first time on an existing database:

```bash
# Check database connection
poetry run poe db-manager check

# Create initial migration from existing schema
poetry run poe db-manager init

# Stamp the database (mark it as up-to-date without running migrations)
poetry run poe db-manager stamp head
```

## Usage

### Poetry Tasks

The following Poetry tasks are available for database migration management:

```bash
# Apply all pending migrations
poetry run poe db-migrate

# Create a new migration
poetry run poe db-migration-create "Add new column to users table"

# Show migration history
poetry run poe db-migration-history

# Show current migration version
poetry run poe db-migration-current

# Downgrade to previous migration
poetry run poe db-migration-downgrade -1

# Migration manager script (more options)
poetry run poe db-manager --help
```

### Migration Manager Script

The `scripts/migration_manager.py` script provides additional functionality:

```bash
# Check database connection
poetry run poe db-manager check

# Create initial migration from existing schema
poetry run poe db-manager init

# Apply migrations
poetry run poe db-manager migrate

# Create new migration
poetry run poe db-manager create "Migration description"

# Show migration history
poetry run poe db-manager history

# Show current version
poetry run poe db-manager current

# Downgrade migration
poetry run poe db-manager downgrade -1

# Stamp database with specific revision
poetry run poe db-manager stamp head

# List existing SQL files
poetry run poe db-manager list-sql
```

### Direct Alembic Commands

You can also use Alembic commands directly:

```bash
# Generate a new migration
poetry run alembic revision --autogenerate -m "Add new table"

# Apply migrations
poetry run alembic upgrade head

# Show current version
poetry run alembic current

# Show history
poetry run alembic history

# Downgrade
poetry run alembic downgrade -1
```

## Migration Workflow

### 1. Making Schema Changes

1. Modify your SQLAlchemy models in `src/olympus_common/db.py`
2. Generate a new migration:
   ```bash
   poetry run poe db-migration-create "Description of changes"
   ```
3. Review the generated migration file in `alembic/versions/`
4. Apply the migration:
   ```bash
   poetry run poe db-migrate
   ```

### 2. Working with Existing SQL Files

If you have existing SQL migration files in `scripts/sql/`, you can:

1. List them: `poetry run poe db-manager list-sql`
2. Create equivalent Alembic migrations manually
3. Use the migration files as reference for creating Alembic migrations

## Configuration

### alembic.ini

The main configuration file with:
- Script location
- File naming template (includes timestamp)
- Logging configuration

### env.py

The environment configuration that:
- Imports your SQLAlchemy models
- Configures database connection using your `DatabaseConfig`
- Sets up schema-aware migrations
- Handles both online and offline migration modes

## Best Practices

1. **Always review generated migrations** before applying them
2. **Test migrations on a copy of production data** before applying to production
3. **Keep migrations small and focused** on specific changes
4. **Use descriptive migration messages**
5. **Never edit applied migrations** - create new ones instead
6. **Backup your database** before applying migrations in production

## Troubleshooting

### Connection Issues

If you get database connection errors:
1. Check your `.env` file configuration
2. Ensure the database server is running
3. Verify network connectivity and credentials
4. Test connection: `poetry run poe db-manager check`

### Migration Issues

If migrations fail:
1. Check the migration file for syntax errors
2. Ensure the database user has necessary permissions
3. Review the error message for specific issues
4. Consider rolling back and fixing the migration

### Schema Issues

If you're using a specific PostgreSQL schema:
- The configuration automatically sets `search_path` to your schema
- Migration version table is created in the specified schema
- All operations respect the schema configuration
