"""OID mapping for Spectrum."""

from olympus_common.base_oid_mapper import BaseOIDMapper


class OIDMapper(BaseOIDMapper):
    """OID mapping for Spectrum."""

    @property
    def mappings(self) -> dict[str, str]:
        """Return the mappings of OIDs to field names."""
        return {
            "status": "status",
            "mtype": "mtype",
            "time": "time",
            "mname": "mname",
            "alarm_state": "alarm_state",
            "date": "date",
            "event_message": "event_message",
            "clearable": "clearable",
            "dtype": "dtype",
            "landscape": "landscape",
            "secstr": "secstr",
            "cause_code": "cause_code",
            "mthandle": "mthandle",
            "severity": "raw_severity",
            "ackd": "ackd",
            "event_type": "raw_event_type",
            "repair_person": "repair_person",
            "ipaddress": "ipaddress",
            "mhandle": "mhandle",
            "alarm_id": "raw_event_id",
        }
