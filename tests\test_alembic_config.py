"""Tests for Alembic configuration."""

import os
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

import pytest

# Add the alembic directory to the path so we can import env.py
alembic_dir = Path(__file__).parent.parent / "alembic"
sys.path.insert(0, str(alembic_dir))


class TestAlembicConfiguration:
    """Test Alembic configuration and environment setup."""

    def test_alembic_env_imports(self):
        """Test that the Alembic env.py can import required modules."""
        # This test verifies that the path setup in env.py works correctly
        try:
            import env
            assert hasattr(env, 'target_metadata')
            assert hasattr(env, 'run_migrations_offline')
            assert hasattr(env, 'run_migrations_online')
        except ImportError as e:
            pytest.fail(f"Failed to import Alembic env module: {e}")

    @patch.dict(os.environ, {
        'DB_HOST': 'localhost',
        'DB_PORT': '5432',
        'DB_NAME': 'test_db',
        'DB_USER': 'test_user',
        'DB_PASSWORD': 'test_pass',
        'DB_SCHEMA': 'test_schema'
    })
    def test_database_config_integration(self):
        """Test that DatabaseConfig can be imported and used in Alembic context."""
        try:
            # Import after setting environment variables
            from olympus_common.config import DatabaseConfig
            
            db_config = DatabaseConfig()
            assert db_config.host == 'localhost'
            assert db_config.port == 5432
            assert db_config.name == 'test_db'
            assert db_config.username == 'test_user'
            assert db_config.password == 'test_pass'
            assert db_config.schema == 'test_schema'
            
            # Test connection string generation
            conn_str = db_config.to_conninfo()
            assert 'postgresql+psycopg://' in conn_str
            assert 'test_user:test_pass@localhost:5432/test_db' in conn_str
            
        except ImportError as e:
            pytest.fail(f"Failed to import DatabaseConfig: {e}")

    def test_sqlalchemy_models_metadata(self):
        """Test that SQLAlchemy models metadata is accessible."""
        try:
            from olympus_common.db import Base
            
            # Check that Base has metadata
            assert hasattr(Base, 'metadata')
            assert Base.metadata is not None
            
            # Check that there are tables defined
            tables = Base.metadata.tables
            assert len(tables) > 0, "No tables found in Base.metadata"
            
            # Verify some expected tables exist
            expected_tables = ['agent', 'alarm', 'occurrence', 'enrichment']
            for table_name in expected_tables:
                assert table_name in tables, f"Expected table '{table_name}' not found"
                
        except ImportError as e:
            pytest.fail(f"Failed to import Base from olympus_common.db: {e}")

    def test_alembic_ini_exists(self):
        """Test that alembic.ini file exists and has required configuration."""
        alembic_ini = Path(__file__).parent.parent / "alembic.ini"
        assert alembic_ini.exists(), "alembic.ini file not found"
        
        content = alembic_ini.read_text()
        
        # Check for required sections
        assert "[alembic]" in content
        assert "script_location" in content
        assert "file_template" in content
        
        # Check that database URL is commented out (we use dynamic configuration)
        lines = content.split('\n')
        db_url_lines = [line for line in lines if 'sqlalchemy.url' in line and not line.strip().startswith('#')]
        assert len(db_url_lines) == 0, "sqlalchemy.url should be commented out in alembic.ini"

    def test_migration_manager_script_exists(self):
        """Test that the migration manager script exists and is executable."""
        script_path = Path(__file__).parent.parent / "scripts" / "migration_manager.py"
        assert script_path.exists(), "migration_manager.py script not found"
        
        # Check that the script has the main function
        content = script_path.read_text()
        assert "def main():" in content
        assert "if __name__ == \"__main__\":" in content

    def test_env_example_exists(self):
        """Test that .env.example file exists with required variables."""
        env_example = Path(__file__).parent.parent / ".env.example"
        assert env_example.exists(), ".env.example file not found"
        
        content = env_example.read_text()
        
        # Check for required environment variables
        required_vars = ['DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD', 'DB_SCHEMA']
        for var in required_vars:
            assert var in content, f"Required environment variable {var} not found in .env.example"

    @patch('alembic.context.configure')
    @patch('alembic.context.begin_transaction')
    @patch.dict(os.environ, {
        'DB_HOST': 'localhost',
        'DB_PORT': '5432',
        'DB_NAME': 'test_db',
        'DB_USER': 'test_user',
        'DB_PASSWORD': 'test_pass',
        'DB_SCHEMA': 'test_schema'
    })
    def test_offline_migration_configuration(self, mock_begin_transaction, mock_configure):
        """Test that offline migration configuration works correctly."""
        try:
            import env
            
            # Mock the context
            mock_context = MagicMock()
            mock_begin_transaction.return_value.__enter__ = MagicMock()
            mock_begin_transaction.return_value.__exit__ = MagicMock()
            
            # Call the offline migration function
            env.run_migrations_offline()
            
            # Verify that configure was called with correct parameters
            mock_configure.assert_called_once()
            call_args = mock_configure.call_args
            
            # Check that URL contains our test database configuration
            assert 'url' in call_args.kwargs
            url = call_args.kwargs['url']
            assert 'postgresql+psycopg://' in url
            assert 'test_user:test_pass@localhost:5432/test_db' in url
            
            # Check that schema is configured
            assert 'version_table_schema' in call_args.kwargs
            assert call_args.kwargs['version_table_schema'] == 'test_schema'
            
        except Exception as e:
            pytest.fail(f"Offline migration configuration failed: {e}")
