"""Entrypoint for the application."""

from mon_scada import dd
from mon_scada.config import config
from mon_scada.oid_mapper import OIDMapper
from olympus_common import defaults

application = defaults.databasewriter_kafkareader_app(config=config, suffix_service_name=config.scada_cc)


@application.run_forever(config.sleep_time)
def main(data: list[dict]) -> list[dict]:
    """Execute the main function for when the project is run."""
    return defaults.transform(data, config, dd.run, application.backend_db_session, oid_mapper=OIDMapper)
