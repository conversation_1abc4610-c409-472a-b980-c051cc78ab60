"""Datawriters for ing-zabbix."""

import json
import logging
from dataclasses import dataclass

from confluent_kafka import Producer

from olympus_common.config import KafkaProducerConfig
from olympus_common.dataclass import dataclass_field
from olympus_common.datawriters import DataWriter


@dataclass
class KafkaWriter(DataWriter):
    """Represent a KafkaWriter.

    This concrete DataWriter publishes the data to kafka on success and closes the producer on error.
    """

    config: KafkaProducerConfig = dataclass_field(KafkaProducerConfig)

    _producer: Producer | None = None

    def success(self, results: list[dict]) -> None:
        """Print data on success."""
        for json_message in results:
            json_message_dumped = json.dumps(json_message).encode(self.config.message_encoding)
            for topic in self.config.topics:  # Support sending to multiple topics
                self.producer.produce(topic, json_message_dumped)
        self.producer.flush()
        logging.info(f"Produced {len(results)} messages to {self.config.topics}")

    def error(self, _: list[dict], __: Exception) -> None:
        """Reset the producer to force the creation of a new one for the next run."""
        self.producer.flush(self.config.flush_timeout)
        self._producer = None

    @property
    def producer(self):
        """Return the producer, creating it if it doesn't exist."""
        if not self._producer:
            conf = {
                "bootstrap.servers": ",".join(self.config.bootstrap_servers),
                "security.protocol": self.config.security_protocol,
                "sasl.mechanism": self.config.sasl_mechanism,
                "sasl.username": self.config.user.lower(),
                "sasl.password": self.config.password,
                "max.in.flight.requests.per.connection": self.config.max_in_flight_requests_per_connection,
                "retries": self.config.retries,
            }
            self._producer = Producer(conf)
        return self._producer
