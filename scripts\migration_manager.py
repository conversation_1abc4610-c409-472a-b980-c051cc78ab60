#!/usr/bin/env python3
"""Migration management script for Alembic integration.

This script helps manage database migrations using Alembic while working
with existing SQL migration files.
"""

import argparse
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from olympus_common.db import create_session


def create_initial_migration() -> None:
    """Create an initial migration from the current database schema."""
    print("Creating initial migration from current database schema...")

    # This will create a migration that represents the current state
    os.system("poetry run alembic revision --autogenerate -m 'Initial migration from existing schema'")

    print("Initial migration created successfully!")
    print("Note: Review the generated migration file before applying it.")


def apply_migrations() -> None:
    """Apply all pending migrations."""
    print("Applying database migrations...")
    os.system("poetry run alembic upgrade head")
    print("Migrations applied successfully!")


def create_migration(message: str) -> None:
    """Create a new migration with the given message."""
    print(f"Creating new migration: {message}")
    os.system(f'poetry run alembic revision --autogenerate -m "{message}"')
    print("Migration created successfully!")


def show_migration_history() -> None:
    """Show the migration history."""
    print("Migration history:")
    os.system("poetry run alembic history")


def show_current_version() -> None:
    """Show the current migration version."""
    print("Current migration version:")
    os.system("poetry run alembic current")


def downgrade_migration(target: str = "-1") -> None:
    """Downgrade to a specific migration."""
    print(f"Downgrading to: {target}")
    os.system(f"poetry run alembic downgrade {target}")
    print("Downgrade completed!")


def stamp_database(revision: str = "head") -> None:
    """Stamp the database with a specific revision without running migrations."""
    print(f"Stamping database with revision: {revision}")
    os.system(f"poetry run alembic stamp {revision}")
    print("Database stamped successfully!")


def check_database_connection() -> bool:
    """Check if we can connect to the database."""
    try:
        session = create_session("migration_manager")
        session.execute("SELECT 1")
        session.close()
        print("✓ Database connection successful")
        return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False


def list_sql_files() -> None:
    """List existing SQL migration files."""
    sql_dir = Path(__file__).parent / "sql"
    if not sql_dir.exists():
        print("No SQL directory found")
        return

    print("Existing SQL migration files:")
    for sql_file in sorted(sql_dir.glob("*.sql")):
        print(f"  - {sql_file.name}")


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(description="Database migration management")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Check connection
    subparsers.add_parser("check", help="Check database connection")

    # Initial setup
    subparsers.add_parser("init", help="Create initial migration from existing schema")

    # Migration operations
    subparsers.add_parser("migrate", help="Apply all pending migrations")

    create_parser = subparsers.add_parser("create", help="Create a new migration")
    create_parser.add_argument("message", help="Migration message")

    # History and status
    subparsers.add_parser("history", help="Show migration history")
    subparsers.add_parser("current", help="Show current migration version")

    # Downgrade
    downgrade_parser = subparsers.add_parser("downgrade", help="Downgrade migration")
    downgrade_parser.add_argument("target", nargs="?", default="-1", help="Target revision (default: -1)")

    # Stamp
    stamp_parser = subparsers.add_parser("stamp", help="Stamp database with revision")
    stamp_parser.add_argument("revision", nargs="?", default="head", help="Revision to stamp (default: head)")

    # List SQL files
    subparsers.add_parser("list-sql", help="List existing SQL migration files")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # Execute the appropriate command
    if args.command == "check":
        check_database_connection()
    elif args.command == "init":
        if check_database_connection():
            create_initial_migration()
    elif args.command == "migrate":
        if check_database_connection():
            apply_migrations()
    elif args.command == "create":
        if check_database_connection():
            create_migration(args.message)
    elif args.command == "history":
        show_migration_history()
    elif args.command == "current":
        show_current_version()
    elif args.command == "downgrade":
        if check_database_connection():
            downgrade_migration(args.target)
    elif args.command == "stamp":
        if check_database_connection():
            stamp_database(args.revision)
    elif args.command == "list-sql":
        list_sql_files()


if __name__ == "__main__":
    main()
